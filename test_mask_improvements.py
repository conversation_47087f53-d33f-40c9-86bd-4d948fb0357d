#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试掩膜改进功能
- 掩膜重叠过滤
- 透明背景对象提取
"""

import os
import sys
import cv2
import numpy as np
from PIL import Image
import argparse

def create_test_image():
    """创建测试图像，包含重叠的对象"""
    # 创建一个简单的测试图像
    image = np.zeros((400, 600, 3), dtype=np.uint8)
    image.fill(255)  # 白色背景

    # 绘制一些重叠的圆形和矩形
    # 大圆
    cv2.circle(image, (150, 150), 80, (255, 0, 0), -1)  # 蓝色圆
    # 小圆（重叠）
    cv2.circle(image, (180, 120), 40, (0, 255, 0), -1)  # 绿色圆

    # 大矩形
    cv2.rectangle(image, (300, 100), (500, 250), (0, 0, 255), -1)  # 红色矩形
    # 小矩形（重叠）
    cv2.rectangle(image, (350, 150), (450, 200), (255, 255, 0), -1)  # 黄色矩形

    # 独立的对象
    cv2.circle(image, (100, 300), 50, (128, 0, 128), -1)  # 紫色圆
    cv2.rectangle(image, (400, 300), (550, 380), (0, 128, 128), -1)  # 青色矩形

    return image

def create_whole_image_test():
    """创建包含全图掩膜的测试图像"""
    # 创建一个图像，其中包含一个几乎覆盖整个图像的大对象
    image = np.zeros((400, 600, 3), dtype=np.uint8)
    image.fill(255)  # 白色背景

    # 创建一个几乎覆盖整个图像的大矩形（90%面积）
    margin = 20
    cv2.rectangle(image, (margin, margin), (600-margin, 400-margin), (100, 100, 100), -1)  # 灰色大矩形

    # 在大矩形内添加一些小对象
    cv2.circle(image, (150, 150), 30, (255, 0, 0), -1)  # 蓝色圆
    cv2.circle(image, (450, 150), 30, (0, 255, 0), -1)  # 绿色圆
    cv2.rectangle(image, (250, 250), (350, 320), (0, 0, 255), -1)  # 红色矩形

    return image

def test_whole_image_filtering():
    """测试全图掩膜过滤功能"""
    print("🧪 测试全图掩膜过滤功能...")

    try:
        from sam_everything_gpu import GPUAcceleratedSAMEverything

        # 创建包含全图掩膜的测试图像
        test_image = create_whole_image_test()
        test_image_path = "test_whole_image.jpg"
        cv2.imwrite(test_image_path, test_image)
        print(f"✅ 创建全图测试图像: {test_image_path}")

        # 创建配置（较严格的全图过滤）
        config = {
            'checkpoint_path': 'sam_vit_h_4b8939.pth',
            'device': 'auto',
            'points_per_side': 16,
            'min_object_area': 50,
            'overlap_threshold': 0.1,
            'max_mask_ratio': 0.7,  # 70% 阈值，应该过滤掉大背景
            'use_mask_extraction': True,
            'generate_yolo_labels': True,
        }

        # 初始化SAM Everything
        sam_everything = GPUAcceleratedSAMEverything(config)

        if sam_everything.sam_generator is None:
            print("❌ SAM模型未初始化，跳过测试")
            return False

        # 处理图像
        output_dir = "test_whole_image_output"
        result = sam_everything.process_image(test_image, test_image_path, output_dir)

        if result['success']:
            print(f"✅ 全图过滤测试成功!")
            print(f"   原始掩膜数量: {result.get('original_masks_count', 'N/A')}")
            print(f"   全图掩膜移除: {result.get('whole_image_removed_count', 0)}")
            print(f"   最终对象数量: {result['objects_count']}")
            print(f"   处理时间: {result['processing_time']:.2f}秒")

            # 验证是否成功过滤了全图掩膜
            whole_image_removed = result.get('whole_image_removed_count', 0)
            if whole_image_removed > 0:
                print(f"   ✅ 成功过滤 {whole_image_removed} 个全图掩膜")
            else:
                print(f"   ⚠️ 未检测到全图掩膜（可能是正常的）")

            return True
        else:
            print(f"❌ 处理失败: {result.get('error', '未知错误')}")
            return False

    except ImportError:
        print("❌ 无法导入GPU加速SAM模块")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_overlap_filtering():
    """测试重叠过滤功能"""
    print("\n🧪 测试掩膜重叠过滤功能...")

    try:
        from sam_everything_gpu import GPUAcceleratedSAMEverything

        # 创建测试图像
        test_image = create_test_image()
        test_image_path = "test_overlap_image.jpg"
        cv2.imwrite(test_image_path, test_image)
        print(f"✅ 创建测试图像: {test_image_path}")

        # 创建配置
        config = {
            'checkpoint_path': 'sam_vit_h_4b8939.pth',
            'device': 'auto',
            'points_per_side': 16,  # 较少的点以加快测试
            'min_object_area': 50,
            'overlap_threshold': 0.1,  # 10% 重叠阈值
            'max_mask_ratio': 0.8,     # 80% 全图阈值
            'use_mask_extraction': True,
            'generate_yolo_labels': True,
        }
        
        # 初始化SAM Everything
        sam_everything = GPUAcceleratedSAMEverything(config)
        
        if sam_everything.sam_generator is None:
            print("❌ SAM模型未初始化，跳过测试")
            return False
        
        # 处理图像
        output_dir = "test_overlap_output"
        result = sam_everything.process_image(test_image, test_image_path, output_dir)
        
        if result['success']:
            print(f"✅ 处理成功!")
            print(f"   原始掩膜数量: {result.get('original_masks_count', 'N/A')}")
            print(f"   过滤后数量: {result.get('filtered_masks_count', 'N/A')}")
            print(f"   最终对象数量: {result['objects_count']}")
            print(f"   处理时间: {result['processing_time']:.2f}秒")
            print(f"   输出目录: {result['output_dir']}")
            
            # 检查生成的文件
            if os.path.exists(output_dir):
                files = os.listdir(output_dir)
                png_files = [f for f in files if f.endswith('.png')]
                txt_files = [f for f in files if f.endswith('.txt')]
                
                print(f"   生成的PNG文件: {len(png_files)}")
                print(f"   生成的YOLO标签: {len(txt_files)}")
                
                # 检查PNG文件是否有透明度
                if png_files:
                    sample_png = os.path.join(output_dir, png_files[0])
                    try:
                        pil_img = Image.open(sample_png)
                        if pil_img.mode == 'RGBA':
                            print(f"   ✅ PNG文件支持透明度")
                        else:
                            print(f"   ⚠️ PNG文件不支持透明度")
                    except Exception as e:
                        print(f"   ❌ 检查PNG透明度失败: {e}")
            
            return True
        else:
            print(f"❌ 处理失败: {result.get('error', '未知错误')}")
            return False
            
    except ImportError:
        print("❌ 无法导入GPU加速SAM模块")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_mask_extraction_comparison():
    """比较掩膜提取和矩形裁剪的效果"""
    print("\n🔍 比较掩膜提取和矩形裁剪...")
    
    try:
        from sam_everything_gpu import GPUAcceleratedSAMEverything
        
        # 创建测试图像
        test_image = create_test_image()
        test_image_path = "test_extraction_image.jpg"
        cv2.imwrite(test_image_path, test_image)
        
        # 测试掩膜提取
        config_mask = {
            'checkpoint_path': 'sam_vit_h_4b8939.pth',
            'device': 'auto',
            'points_per_side': 16,
            'min_object_area': 100,
            'overlap_threshold': 0.1,
            'use_mask_extraction': True,
            'generate_yolo_labels': False,
        }
        
        sam_mask = GPUAcceleratedSAMEverything(config_mask)
        if sam_mask.sam_generator is None:
            print("❌ SAM模型未初始化")
            return False
        
        # 处理图像（掩膜提取）
        result_mask = sam_mask.process_image(test_image, test_image_path, "test_mask_extraction")
        
        if result_mask['success']:
            print(f"✅ 掩膜提取完成:")
            print(f"   对象数量: {result_mask['objects_count']}")
            print(f"   处理时间: {result_mask['processing_time']:.2f}秒")
            
            # 检查文件格式
            output_dir = result_mask['output_dir']
            if os.path.exists(output_dir):
                files = os.listdir(output_dir)
                png_files = [f for f in files if f.endswith('.png')]
                print(f"   PNG文件数量: {len(png_files)}")
        
        return True
        
    except Exception as e:
        print(f"❌ 比较测试失败: {e}")
        return False

def test_cli_parameters():
    """测试CLI新参数"""
    print("\n🖥️ 测试CLI新参数...")

    # 显示新的CLI参数
    print("新增的CLI参数:")
    print("  --overlap-threshold FLOAT    掩膜重叠过滤阈值 (默认: 0.1)")
    print("  --max-mask-ratio FLOAT       最大掩膜面积比例阈值 (默认: 0.8)")
    print("  --no-mask-extraction         禁用掩膜提取，使用矩形裁剪")

    # 示例命令
    print("\n示例命令:")
    print("# 使用掩膜提取和双重过滤")
    print("python sam_gpu_cli.py --device gpu --input image.jpg --output results \\")
    print("    --overlap-threshold 0.15 --max-mask-ratio 0.7 --min-area 200")

    print("\n# 严格的全图掩膜过滤")
    print("python sam_gpu_cli.py --device gpu --input image.jpg --output results \\")
    print("    --max-mask-ratio 0.6 --overlap-threshold 0.1")

    print("\n# 禁用掩膜提取，使用传统矩形裁剪")
    print("python sam_gpu_cli.py --device gpu --input image.jpg --output results \\")
    print("    --no-mask-extraction --max-mask-ratio 0.8")

    print("\n# 宽松过滤设置（保留更多对象）")
    print("python sam_gpu_cli.py --device gpu --input image.jpg --output results \\")
    print("    --overlap-threshold 0.2 --max-mask-ratio 0.9 --min-area 50")

    return True

def main():
    """主测试函数"""
    parser = argparse.ArgumentParser(description='测试掩膜改进功能')
    parser.add_argument('--skip-sam', action='store_true', help='跳过SAM相关测试')
    parser.add_argument('--test-cli', action='store_true', help='只测试CLI参数')
    
    args = parser.parse_args()
    
    print("🚀 掩膜改进功能测试")
    print("=" * 50)
    
    success_count = 0
    total_tests = 0
    
    if args.test_cli:
        total_tests += 1
        if test_cli_parameters():
            success_count += 1
    else:
        if not args.skip_sam:
            # 检查模型文件
            if not os.path.exists('sam_vit_h_4b8939.pth'):
                print("⚠️ SAM模型文件不存在，跳过SAM相关测试")
                print("   请下载模型文件: wget https://dl.fbaipublicfiles.com/segment_anything/sam_vit_h_4b8939.pth")
                args.skip_sam = True
        
        if not args.skip_sam:
            # 测试全图掩膜过滤
            total_tests += 1
            if test_whole_image_filtering():
                success_count += 1

            # 测试重叠过滤
            total_tests += 1
            if test_overlap_filtering():
                success_count += 1

            # 测试掩膜提取比较
            total_tests += 1
            if test_mask_extraction_comparison():
                success_count += 1
        
        # 测试CLI参数
        total_tests += 1
        if test_cli_parameters():
            success_count += 1
    
    # 总结
    print("\n" + "=" * 50)
    print(f"📊 测试结果: {success_count}/{total_tests} 项测试通过")
    
    if success_count == total_tests:
        print("🎉 所有测试通过！掩膜改进功能正常工作")
        print("\n✨ 新功能特性:")
        print("  ✅ 全图掩膜过滤 - 防止单个掩膜覆盖整个图像")
        print("  ✅ 掩膜重叠过滤 - 移除冗余的小分割")
        print("  ✅ 透明背景提取 - PNG格式保存，背景透明")
        print("  ✅ CLI参数支持 - 命令行控制新功能")
        print("  ✅ GUI界面集成 - 图形界面支持新参数")
    else:
        print("⚠️ 部分测试失败，请检查错误信息")
    
    return success_count == total_tests

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
