#!/usr/bin/env python3
"""
测试GUI启动修复
"""

import sys
import os

def test_gui_startup():
    """测试GUI启动"""
    print("🚀 测试GUI启动...")
    
    try:
        # 导入必要的模块
        import tkinter as tk
        from tkinter import ttk
        
        # 设置测试环境
        os.environ['DISPLAY'] = ':0.0'  # 如果在Linux环境下
        
        # 创建测试根窗口
        root = tk.Tk()
        root.withdraw()  # 隐藏窗口进行测试
        
        print("✅ Tkinter导入成功")
        
        # 测试导入主GUI类
        try:
            from enhanced_segmentation_gui import EnhancedSegmentationGUI
            print("✅ 主GUI类导入成功")
        except Exception as e:
            print(f"❌ 主GUI类导入失败: {e}")
            return False
        
        # 测试创建GUI实例（不显示）
        try:
            app = EnhancedSegmentationGUI(root)
            print("✅ GUI实例创建成功")
            
            # 检查关键属性
            if hasattr(app, 'classification_manager'):
                print("✅ 分类管理器属性存在")
            else:
                print("⚠️ 分类管理器属性不存在")
            
            if hasattr(app, 'status_var'):
                print("✅ 状态变量存在")
            else:
                print("⚠️ 状态变量不存在")
            
            # 测试日志方法
            try:
                app.log_message("测试日志消息")
                print("✅ 日志方法工作正常")
            except Exception as e:
                print(f"❌ 日志方法失败: {e}")
                return False
            
        except Exception as e:
            print(f"❌ GUI实例创建失败: {e}")
            return False
        
        # 清理
        root.destroy()
        
        return True
        
    except Exception as e:
        print(f"❌ GUI启动测试失败: {e}")
        return False

def check_initialization_order():
    """检查初始化顺序"""
    print("\n🔄 检查初始化顺序...")
    
    try:
        with open('enhanced_segmentation_gui.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找关键代码行
        lines = content.split('\n')
        
        initialize_line = -1
        create_widgets_line = -1
        log_message_fix_line = -1
        
        for i, line in enumerate(lines):
            if 'self.initialize_systems()' in line and 'def' not in line:
                initialize_line = i + 1
            elif 'self.create_widgets()' in line and 'def' not in line:
                create_widgets_line = i + 1
            elif 'hasattr(self, \'status_var\')' in line:
                log_message_fix_line = i + 1
        
        print(f"initialize_systems() 在第 {initialize_line} 行")
        print(f"create_widgets() 在第 {create_widgets_line} 行")
        print(f"log_message 修复在第 {log_message_fix_line} 行")
        
        if initialize_line > 0 and create_widgets_line > 0:
            if initialize_line < create_widgets_line:
                print("✅ 初始化顺序正确")
            else:
                print("❌ 初始化顺序错误")
                return False
        
        if log_message_fix_line > 0:
            print("✅ log_message 方法已修复")
        else:
            print("❌ log_message 方法未修复")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 检查初始化顺序失败: {e}")
        return False

def check_error_handling():
    """检查错误处理"""
    print("\n🛡️ 检查错误处理...")
    
    try:
        with open('enhanced_segmentation_gui.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查关键的错误处理代码
        checks = [
            ('hasattr(self, \'classification_manager\')', '分类管理器属性检查'),
            ('hasattr(self, \'status_var\')', '状态变量属性检查'),
            ('except Exception as e:', '异常处理'),
            ('self.log_message(f"', '日志记录'),
        ]
        
        for check_text, description in checks:
            if check_text in content:
                print(f"✅ {description}")
            else:
                print(f"❌ 缺少{description}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ 检查错误处理失败: {e}")
        return False

def main():
    print("🔧 GUI启动修复验证")
    print("=" * 40)
    
    tests = [
        ("初始化顺序检查", check_initialization_order),
        ("错误处理检查", check_error_handling),
        ("GUI启动测试", test_gui_startup),
    ]
    
    passed = 0
    for test_name, test_func in tests:
        print(f"\n{test_name}:")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 通过")
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
    
    print(f"\n📊 结果: {passed}/{len(tests)} 项测试通过")
    
    if passed == len(tests):
        print("\n🎉 GUI启动修复成功！")
        print("\n✅ 修复内容:")
        print("- 调整了初始化顺序")
        print("- 修复了log_message方法")
        print("- 添加了属性检查")
        print("- 改进了错误处理")
        print("\n🚀 现在可以正常启动GUI了！")
        print("   python enhanced_segmentation_gui.py")
    else:
        print("\n⚠️ 部分问题仍需解决")
    
    return passed == len(tests)

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
