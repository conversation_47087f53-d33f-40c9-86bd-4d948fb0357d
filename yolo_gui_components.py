#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
YOLO GUI组件
为主GUI提供YOLO训练和识别功能的界面组件
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import threading
import os
import json
from typing import Dict, Optional, Callable
from yolo_manager import YOLOManager

class YOLOModelSelector(ttk.Frame):
    """YOLO模型选择器"""

    def __init__(self, parent, yolo_manager: YOLOManager, **kwargs):
        super().__init__(parent, **kwargs)
        self.yolo_manager = yolo_manager
        self.selected_model = tk.StringVar()
        self.download_progress = tk.DoubleVar()

        self.create_widgets()
        self.refresh_models()

    def create_widgets(self):
        """创建界面组件"""
        # 模型选择框架
        model_frame = ttk.LabelFrame(self, text="选择YOLO模型")
        model_frame.pack(fill=tk.X, padx=5, pady=5)

        # 模型下拉列表
        self.model_combo = ttk.Combobox(model_frame, textvariable=self.selected_model,
                                       state="readonly", width=30)
        self.model_combo.pack(side=tk.LEFT, padx=5, pady=5)
        self.model_combo.bind('<<ComboboxSelected>>', self.on_model_selected)

        # 刷新按钮
        ttk.Button(model_frame, text="刷新", command=self.refresh_models).pack(side=tk.LEFT, padx=5)

        # 下载按钮
        self.download_btn = ttk.Button(model_frame, text="下载模型", command=self.download_model)
        self.download_btn.pack(side=tk.LEFT, padx=5)

        # 上传自定义模型按钮
        self.upload_btn = ttk.Button(model_frame, text="上传自定义模型", command=self.upload_custom_model)
        self.upload_btn.pack(side=tk.LEFT, padx=5)

        # 模型信息框架
        info_frame = ttk.LabelFrame(self, text="模型信息")
        info_frame.pack(fill=tk.X, padx=5, pady=5)

        # 模型信息文本
        self.info_text = tk.Text(info_frame, height=4, wrap=tk.WORD)
        self.info_text.pack(fill=tk.X, padx=5, pady=5)

        # 下载进度条
        self.progress_bar = ttk.Progressbar(self, variable=self.download_progress,
                                          maximum=100, mode='determinate')
        self.progress_bar.pack(fill=tk.X, padx=5, pady=5)
        self.progress_bar.pack_forget()  # 初始隐藏

        # 状态标签
        self.status_label = ttk.Label(self, text="")
        self.status_label.pack(pady=2)

    def refresh_models(self):
        """刷新模型列表"""
        models = self.yolo_manager.get_available_models()
        model_options = []

        for model_id, info in models.items():
            status = "✅" if info['downloaded'] else "⬇️"
            option = f"{status} {model_id} - {info['name']} ({info['size']})"
            model_options.append(option)

        self.model_combo['values'] = model_options
        if model_options and not self.selected_model.get():
            self.model_combo.current(0)
            self.on_model_selected()

    def on_model_selected(self, event=None):
        """模型选择事件"""
        selection = self.selected_model.get()
        if not selection:
            return

        # 提取模型ID
        model_id = selection.split(' - ')[0].split(' ', 1)[1]
        models = self.yolo_manager.get_available_models()

        if model_id in models:
            model_info = models[model_id]

            # 更新信息显示
            info_text = f"名称: {model_info['name']}\n"
            info_text += f"描述: {model_info['description']}\n"
            info_text += f"大小: {model_info['size']} | 速度: {model_info['speed']} | 精度: {model_info['accuracy']}\n"
            info_text += f"状态: {'已下载' if model_info['downloaded'] else '需要下载'}"

            self.info_text.delete(1.0, tk.END)
            self.info_text.insert(1.0, info_text)

            # 更新下载按钮状态
            if model_info['downloaded']:
                self.download_btn.config(text="已下载", state="disabled")
            else:
                self.download_btn.config(text="下载模型", state="normal")

        # 检查是否是自定义模型
        elif hasattr(self, 'custom_models') and model_id in self.custom_models:
            model_info = self.custom_models[model_id]

            # 更新信息显示
            info_text = f"名称: {model_info['name']}\n"
            info_text += f"描述: {model_info['description']}\n"
            info_text += f"路径: {model_info['path']}\n"
            info_text += f"状态: 自定义训练模型"

            self.info_text.delete(1.0, tk.END)
            self.info_text.insert(1.0, info_text)

            # 自定义模型不需要下载
            self.download_btn.config(text="自定义模型", state="disabled")

    def download_model(self):
        """下载选中的模型"""
        selection = self.selected_model.get()
        if not selection:
            return

        model_id = selection.split(' - ')[0].split(' ', 1)[1]

        # 显示进度条
        self.progress_bar.pack(fill=tk.X, padx=5, pady=5)
        self.download_btn.config(state="disabled")
        self.status_label.config(text="正在下载...")

        def progress_callback(downloaded, total, percentage):
            self.download_progress.set(percentage)
            self.status_label.config(text=f"下载中... {percentage:.1f}%")

        def download_thread():
            success = self.yolo_manager.download_model(model_id, progress_callback)

            # 更新UI
            self.after(0, lambda: self.download_complete(success))

        threading.Thread(target=download_thread, daemon=True).start()

    def download_complete(self, success: bool):
        """下载完成回调"""
        self.progress_bar.pack_forget()

        if success:
            self.status_label.config(text="下载完成！")
            self.refresh_models()
            self.on_model_selected()
        else:
            self.status_label.config(text="下载失败！")
            self.download_btn.config(state="normal")

        # 3秒后清除状态
        self.after(3000, lambda: self.status_label.config(text=""))

    def get_selected_model_id(self) -> Optional[str]:
        """获取选中的模型ID"""
        selection = self.selected_model.get()
        if not selection:
            return None

        model_id = selection.split(' - ')[0].split(' ', 1)[1]
        models = self.yolo_manager.get_available_models()

        # 检查是否是标准模型且已下载
        if model_id in models and models[model_id]['downloaded']:
            return model_id

        # 检查是否是自定义模型
        if hasattr(self, 'custom_models') and model_id in self.custom_models:
            return model_id

        return None

    def get_selected_model_path(self) -> Optional[str]:
        """获取选中模型的路径"""
        model_id = self.get_selected_model_id()
        if model_id:
            models = self.yolo_manager.get_available_models()
            if model_id in models:
                return models[model_id]['local_path']

            # 检查是否是自定义模型
            if hasattr(self, 'custom_models') and model_id in self.custom_models:
                return self.custom_models[model_id]['path']
        return None

    def add_custom_model(self, model_path: str, model_name: str):
        """添加自定义模型到选择器"""
        if not hasattr(self, 'custom_models'):
            self.custom_models = {}

        # 生成唯一ID
        custom_id = f"custom_{len(self.custom_models)}"

        self.custom_models[custom_id] = {
            'path': model_path,
            'name': model_name,
            'description': '训练好的自定义模型'
        }

        # 更新下拉列表
        current_values = list(self.model_combo['values'])
        custom_option = f"✅ {custom_id} - {model_name} (自定义)"
        current_values.append(custom_option)
        self.model_combo['values'] = current_values

        # 选择新添加的模型
        self.model_combo.set(custom_option)
        self.on_model_selected()

    def upload_custom_model(self):
        """上传自定义模型"""
        # 选择模型文件
        file_path = filedialog.askopenfilename(
            title="选择自定义YOLO模型文件",
            filetypes=[
                ("YOLO模型文件", "*.pt"),
                ("所有文件", "*.*")
            ]
        )

        if not file_path:
            return

        if not os.path.exists(file_path):
            messagebox.showerror("错误", "选择的文件不存在")
            return

        # 获取模型名称
        default_name = os.path.splitext(os.path.basename(file_path))[0]

        # 创建输入对话框
        dialog = tk.Toplevel(self.winfo_toplevel())
        dialog.title("自定义模型信息")
        dialog.geometry("400x200")
        dialog.transient(self.winfo_toplevel())
        dialog.grab_set()

        # 居中显示
        dialog.update_idletasks()
        x = (dialog.winfo_screenwidth() // 2) - (dialog.winfo_width() // 2)
        y = (dialog.winfo_screenheight() // 2) - (dialog.winfo_height() // 2)
        dialog.geometry(f"+{x}+{y}")

        # 模型名称输入
        ttk.Label(dialog, text="模型名称:").pack(pady=10)
        name_var = tk.StringVar(value=default_name)
        name_entry = ttk.Entry(dialog, textvariable=name_var, width=40)
        name_entry.pack(pady=5)
        name_entry.focus()

        # 模型路径显示
        ttk.Label(dialog, text="模型路径:").pack(pady=(20, 5))
        path_label = ttk.Label(dialog, text=file_path, wraplength=350)
        path_label.pack(pady=5)

        # 按钮框架
        button_frame = ttk.Frame(dialog)
        button_frame.pack(pady=20)

        def confirm_upload():
            model_name = name_var.get().strip()
            if not model_name:
                messagebox.showerror("错误", "请输入模型名称")
                return

            # 添加自定义模型
            self.add_custom_model(file_path, model_name)
            messagebox.showinfo("成功", f"自定义模型 '{model_name}' 已添加成功！")
            dialog.destroy()

        def cancel_upload():
            dialog.destroy()

        ttk.Button(button_frame, text="确定", command=confirm_upload).pack(side=tk.LEFT, padx=10)
        ttk.Button(button_frame, text="取消", command=cancel_upload).pack(side=tk.LEFT, padx=10)

        # 绑定回车键
        dialog.bind('<Return>', lambda e: confirm_upload())

class YOLOTrainingPanel(ttk.Frame):
    """YOLO训练面板"""

    def __init__(self, parent, yolo_manager: YOLOManager, **kwargs):
        super().__init__(parent, **kwargs)
        self.yolo_manager = yolo_manager
        self.dataset_path = tk.StringVar()
        # 种子检测优化的默认参数
        self.epochs = tk.IntVar(value=50)  # 种子检测通常不需要太多轮次
        self.batch_size = tk.IntVar(value=8)  # 较小的批次大小适合种子检测
        self.img_size = tk.IntVar(value=640)
        self.training_progress = tk.DoubleVar()
        self.seed_detection_mode = tk.BooleanVar(value=True)  # 默认启用种子检测模式

        self.create_widgets()

    def create_widgets(self):
        """创建训练界面组件"""
        # 数据集选择
        dataset_frame = ttk.LabelFrame(self, text="训练数据集")
        dataset_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Entry(dataset_frame, textvariable=self.dataset_path, width=50).pack(side=tk.LEFT, padx=5, pady=5)
        ttk.Button(dataset_frame, text="选择数据集", command=self.select_dataset).pack(side=tk.LEFT, padx=5)

        # 训练模式选择
        mode_frame = ttk.LabelFrame(self, text="训练模式")
        mode_frame.pack(fill=tk.X, padx=5, pady=5)

        mode_grid = ttk.Frame(mode_frame)
        mode_grid.pack(fill=tk.X, padx=5, pady=5)

        ttk.Checkbutton(mode_grid, text="🌱 种子检测专用模式（推荐）",
                       variable=self.seed_detection_mode,
                       command=self.on_mode_change).pack(anchor=tk.W)

        ttk.Label(mode_grid, text="启用后将使用针对种子检测优化的参数和数据增强",
                 font=('Arial', 8), foreground='gray').pack(anchor=tk.W, padx=20)

        # 训练参数
        params_frame = ttk.LabelFrame(self, text="训练参数")
        params_frame.pack(fill=tk.X, padx=5, pady=5)

        # 参数网格
        param_grid = ttk.Frame(params_frame)
        param_grid.pack(fill=tk.X, padx=5, pady=5)

        # 训练轮数
        ttk.Label(param_grid, text="训练轮数:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=2)
        self.epochs_spinbox = ttk.Spinbox(param_grid, from_=10, to=1000, textvariable=self.epochs, width=10)
        self.epochs_spinbox.grid(row=0, column=1, padx=5, pady=2)

        # 批次大小
        ttk.Label(param_grid, text="批次大小:").grid(row=0, column=2, sticky=tk.W, padx=5, pady=2)
        self.batch_spinbox = ttk.Spinbox(param_grid, from_=1, to=64, textvariable=self.batch_size, width=10)
        self.batch_spinbox.grid(row=0, column=3, padx=5, pady=2)

        # 图像尺寸
        ttk.Label(param_grid, text="图像尺寸:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=2)
        ttk.Spinbox(param_grid, from_=320, to=1280, increment=32, textvariable=self.img_size, width=10).grid(row=1, column=1, padx=5, pady=2)

        # 种子检测模式说明
        self.mode_info_label = ttk.Label(param_grid, text="🌱 种子检测模式: 50轮次, 8批次, 专用数据增强",
                                        font=('Arial', 8), foreground='blue')
        self.mode_info_label.grid(row=1, column=2, columnspan=2, sticky=tk.W, padx=5, pady=2)

        # 训练控制
        control_frame = ttk.Frame(self)
        control_frame.pack(fill=tk.X, padx=5, pady=5)

        self.train_btn = ttk.Button(control_frame, text="开始训练", command=lambda: self.start_training(getattr(self, 'model_selector', None)))
        self.train_btn.pack(side=tk.LEFT, padx=5)

        self.stop_btn = ttk.Button(control_frame, text="停止训练", command=self.stop_training, state="disabled")
        self.stop_btn.pack(side=tk.LEFT, padx=5)

        # 训练进度
        progress_frame = ttk.LabelFrame(self, text="训练进度")
        progress_frame.pack(fill=tk.X, padx=5, pady=5)

        self.progress_bar = ttk.Progressbar(progress_frame, variable=self.training_progress,
                                          maximum=100, mode='indeterminate')
        self.progress_bar.pack(fill=tk.X, padx=5, pady=5)

        # 训练日志
        log_frame = ttk.LabelFrame(self, text="训练日志")
        log_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        self.log_text = tk.Text(log_frame, height=8, wrap=tk.WORD)
        log_scrollbar = ttk.Scrollbar(log_frame, orient=tk.VERTICAL, command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=log_scrollbar.set)

        self.log_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5, pady=5)
        log_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

    def select_dataset(self):
        """选择数据集文件"""
        file_path = filedialog.askopenfilename(
            title="选择数据集配置文件",
            filetypes=[("YAML files", "*.yaml"), ("All files", "*.*")]
        )
        if file_path:
            self.dataset_path.set(file_path)

    def on_mode_change(self):
        """种子检测模式切换回调"""
        if self.seed_detection_mode.get():
            # 启用种子检测模式 - 设置优化参数
            self.epochs.set(50)
            self.batch_size.set(8)
            self.mode_info_label.config(text="🌱 种子检测模式: 50轮次, 8批次, 专用数据增强", foreground='blue')
        else:
            # 通用模式 - 恢复默认参数
            self.epochs.set(100)
            self.batch_size.set(16)
            self.mode_info_label.config(text="通用模式: 100轮次, 16批次, 标准数据增强", foreground='gray')

    def start_training(self, model_selector=None):
        """开始训练"""
        if model_selector is None:
            # 尝试从父窗口获取模型选择器
            try:
                parent_window = self.winfo_toplevel()
                if hasattr(parent_window, 'yolo_model_selector'):
                    model_selector = parent_window.yolo_model_selector
                else:
                    messagebox.showerror("错误", "无法找到模型选择器")
                    return
            except:
                messagebox.showerror("错误", "无法找到模型选择器")
                return
        model_id = model_selector.get_selected_model_id()
        if not model_id:
            messagebox.showerror("错误", "请先选择并下载一个模型")
            return

        dataset_file = self.dataset_path.get()
        if not dataset_file or not os.path.exists(dataset_file):
            messagebox.showerror("错误", "请选择有效的数据集配置文件")
            return

        # 更新UI状态
        self.train_btn.config(state="disabled")
        self.stop_btn.config(state="normal")
        self.progress_bar.config(mode='indeterminate')
        self.progress_bar.start()

        self.log_message("开始训练...")
        self.log_message(f"模型: {model_id}")
        self.log_message(f"数据集: {dataset_file}")

        # 显示训练模式信息
        mode_text = "🌱 种子检测专用模式" if self.seed_detection_mode.get() else "通用训练模式"
        self.log_message(f"训练模式: {mode_text}")

        self.log_message(f"参数: epochs={self.epochs.get()}, batch={self.batch_size.get()}, imgsz={self.img_size.get()}")

        if self.seed_detection_mode.get():
            self.log_message("✓ 已启用种子检测优化: 专用数据增强、早停机制、学习率调度")

        def training_thread():
            try:
                # 定义日志回调函数
                def log_callback(message):
                    self.after(0, lambda: self.log_message(message))

                trained_model_path = self.yolo_manager.train_model(
                    model_id=model_id,
                    dataset_yaml=dataset_file,
                    epochs=self.epochs.get(),
                    batch_size=self.batch_size.get(),
                    img_size=self.img_size.get(),
                    log_callback=log_callback
                )

                self.after(0, lambda: self.training_complete(trained_model_path))

            except Exception as e:
                self.after(0, lambda: self.training_error(str(e)))

        threading.Thread(target=training_thread, daemon=True).start()

    def stop_training(self):
        """停止训练"""
        # 注意：实际停止训练需要更复杂的实现
        self.log_message("请求停止训练...")
        self.training_complete(None)

    def training_complete(self, model_path: Optional[str]):
        """训练完成"""
        self.train_btn.config(state="normal")
        self.stop_btn.config(state="disabled")
        self.progress_bar.stop()
        self.progress_bar.config(mode='determinate')

        if model_path:
            self.log_message(f"训练完成！模型保存至: {model_path}")
            messagebox.showinfo("训练完成", f"模型训练完成！\n保存路径: {model_path}")
        else:
            self.log_message("训练被中断或失败")

    def training_error(self, error_msg: str):
        """训练错误"""
        self.train_btn.config(state="normal")
        self.stop_btn.config(state="disabled")
        self.progress_bar.stop()

        self.log_message(f"训练错误: {error_msg}")
        messagebox.showerror("训练错误", f"训练过程中发生错误:\n{error_msg}")

    def log_message(self, message: str):
        """添加日志消息"""
        import datetime
        timestamp = datetime.datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"

        self.log_text.insert(tk.END, log_entry)
        self.log_text.see(tk.END)
        self.update_idletasks()

class YOLOPredictionPanel(ttk.Frame):
    """YOLO预测面板"""

    def __init__(self, parent, yolo_manager: YOLOManager, **kwargs):
        super().__init__(parent, **kwargs)
        self.yolo_manager = yolo_manager
        self.image_path = tk.StringVar()
        self.confidence = tk.DoubleVar(value=0.5)
        self.save_results = tk.BooleanVar(value=True)

        self.create_widgets()

    def create_widgets(self):
        """创建预测界面组件"""
        # 图像选择
        image_frame = ttk.LabelFrame(self, text="选择图像")
        image_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Entry(image_frame, textvariable=self.image_path, width=50).pack(side=tk.LEFT, padx=5, pady=5)
        ttk.Button(image_frame, text="选择图像", command=self.select_image).pack(side=tk.LEFT, padx=5)

        # 预测参数
        params_frame = ttk.LabelFrame(self, text="预测参数")
        params_frame.pack(fill=tk.X, padx=5, pady=5)

        # 置信度
        conf_frame = ttk.Frame(params_frame)
        conf_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Label(conf_frame, text="置信度阈值:").pack(side=tk.LEFT)
        ttk.Scale(conf_frame, from_=0.1, to=1.0, variable=self.confidence,
                 orient=tk.HORIZONTAL, length=200).pack(side=tk.LEFT, padx=5)
        ttk.Label(conf_frame, textvariable=self.confidence).pack(side=tk.LEFT)

        # 保存结果选项
        ttk.Checkbutton(params_frame, text="保存预测结果",
                       variable=self.save_results).pack(anchor=tk.W, padx=5, pady=2)

        # 预测按钮
        self.predict_btn = ttk.Button(self, text="开始预测", command=lambda: self.start_prediction(getattr(self, 'model_selector', None)))
        self.predict_btn.pack(pady=10)

        # 结果显示
        results_frame = ttk.LabelFrame(self, text="预测结果")
        results_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # 创建画布用于显示图像
        self.results_canvas = tk.Canvas(results_frame, bg='white')
        canvas_scrollbar_v = ttk.Scrollbar(results_frame, orient=tk.VERTICAL, command=self.results_canvas.yview)
        canvas_scrollbar_h = ttk.Scrollbar(results_frame, orient=tk.HORIZONTAL, command=self.results_canvas.xview)
        self.results_canvas.configure(yscrollcommand=canvas_scrollbar_v.set, xscrollcommand=canvas_scrollbar_h.set)

        self.results_canvas.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5, pady=5)
        canvas_scrollbar_v.pack(side=tk.RIGHT, fill=tk.Y)
        canvas_scrollbar_h.pack(side=tk.BOTTOM, fill=tk.X)

        # 保存图像引用
        self.prediction_photo = None

    def select_image(self):
        """选择图像文件"""
        file_path = filedialog.askopenfilename(
            title="选择图像文件",
            filetypes=[
                ("Image files", "*.jpg *.jpeg *.png *.bmp *.tiff"),
                ("All files", "*.*")
            ]
        )
        if file_path:
            self.image_path.set(file_path)

    def start_prediction(self, model_selector=None):
        """开始预测"""
        # 优先使用传入的model_selector，然后使用实例属性，最后尝试从父窗口获取
        if model_selector is None:
            if hasattr(self, 'model_selector') and self.model_selector is not None:
                model_selector = self.model_selector
            else:
                # 尝试从父窗口获取模型选择器
                try:
                    parent_window = self.winfo_toplevel()
                    if hasattr(parent_window, 'yolo_model_selector'):
                        model_selector = parent_window.yolo_model_selector
                    else:
                        messagebox.showerror("错误", "无法找到模型选择器")
                        return
                except:
                    messagebox.showerror("错误", "无法找到模型选择器")
                    return

        model_path = model_selector.get_selected_model_path()
        if not model_path:
            messagebox.showerror("错误", "请先选择并下载一个模型")
            return

        image_file = self.image_path.get()
        if not image_file or not os.path.exists(image_file):
            messagebox.showerror("错误", "请选择有效的图像文件")
            return

        self.predict_btn.config(state="disabled")
        self._show_message("正在预测...")

        def prediction_thread():
            try:
                save_dir = "yolo_predictions" if self.save_results.get() else None

                results = self.yolo_manager.predict_image(
                    model_path=model_path,
                    image_path=image_file,
                    confidence=self.confidence.get(),
                    save_dir=save_dir
                )

                self.after(0, lambda: self.prediction_complete(results))

            except Exception as e:
                self.after(0, lambda: self.prediction_error(str(e)))

        threading.Thread(target=prediction_thread, daemon=True).start()

    def prediction_complete(self, results: Optional[Dict]):
        """预测完成"""
        self.predict_btn.config(state="normal")

        if results:
            self.display_results(results)
        else:
            self._show_message("预测失败或无结果")

    def prediction_error(self, error_msg: str):
        """预测错误"""
        self.predict_btn.config(state="normal")
        self._show_message(f"预测错误: {error_msg}")

    def _show_message(self, message: str):
        """在画布上显示文字消息"""
        self.results_canvas.delete("all")
        self.prediction_photo = None

        # 在画布中央显示消息
        canvas_width = self.results_canvas.winfo_width()
        canvas_height = self.results_canvas.winfo_height()

        if canvas_width > 1 and canvas_height > 1:
            self.results_canvas.create_text(
                canvas_width // 2, canvas_height // 2,
                text=message, font=("Arial", 12), fill="red", anchor=tk.CENTER
            )

    def display_results(self, results: Dict):
        """显示预测结果图像"""
        # 清空画布
        self.results_canvas.delete("all")
        self.prediction_photo = None

        # 尝试显示预测结果图像
        if self.save_results.get() and 'output_path' in results and results['output_path']:
            self._display_prediction_image(results['output_path'], results)
        else:
            # 如果没有保存结果或没有输出路径，显示原始图像
            self._display_original_image(results['image_path'], results)

    def _display_prediction_image(self, image_path: str, results: Dict):
        """在画布中显示预测结果图像"""
        if not os.path.exists(image_path):
            self._show_message(f"预测结果图像不存在: {image_path}")
            return

        try:
            from PIL import Image, ImageTk

            # 加载图像
            image = Image.open(image_path)

            # 获取画布尺寸
            self.results_canvas.update_idletasks()
            canvas_width = self.results_canvas.winfo_width()
            canvas_height = self.results_canvas.winfo_height()

            if canvas_width > 1 and canvas_height > 1:
                # 调整图像大小以适应画布
                img_width, img_height = image.size
                scale = min(canvas_width / img_width, canvas_height / img_height, 1.0)

                if scale < 1.0:
                    new_width = int(img_width * scale)
                    new_height = int(img_height * scale)
                    image = image.resize((new_width, new_height), Image.Resampling.LANCZOS)

                # 转换为PhotoImage
                self.prediction_photo = ImageTk.PhotoImage(image)

                # 在画布中央显示图像
                self.results_canvas.create_image(
                    canvas_width // 2, canvas_height // 2,
                    image=self.prediction_photo, anchor=tk.CENTER
                )

        except Exception as e:
            self._show_message(f"无法显示预测图像: {e}")

    def _display_original_image(self, image_path: str, results: Dict):
        """显示原始图像（当没有预测结果图像时）"""
        if not os.path.exists(image_path):
            self._show_message(f"原始图像不存在: {image_path}")
            return

        try:
            from PIL import Image, ImageTk

            # 加载图像
            image = Image.open(image_path)

            # 获取画布尺寸
            self.results_canvas.update_idletasks()
            canvas_width = self.results_canvas.winfo_width()
            canvas_height = self.results_canvas.winfo_height()

            if canvas_width > 1 and canvas_height > 1:
                # 调整图像大小以适应画布
                img_width, img_height = image.size
                scale = min(canvas_width / img_width, canvas_height / img_height, 1.0)

                if scale < 1.0:
                    new_width = int(img_width * scale)
                    new_height = int(img_height * scale)
                    image = image.resize((new_width, new_height), Image.Resampling.LANCZOS)

                # 转换为PhotoImage
                self.prediction_photo = ImageTk.PhotoImage(image)

                # 在画布中央显示图像
                self.results_canvas.create_image(
                    canvas_width // 2, canvas_height // 2,
                    image=self.prediction_photo, anchor=tk.CENTER
                )

        except Exception as e:
            self._show_message(f"无法显示图像: {e}")

