#!/usr/bin/env python3
"""
测试GUI初始化修复
"""

import sys
import os

def test_imports():
    """测试导入"""
    print("测试导入...")
    
    try:
        from classification_manager import ClassificationManager
        print("✅ ClassificationManager")
    except Exception as e:
        print(f"❌ ClassificationManager: {e}")
        return False
    
    try:
        from classification_trainer import ClassificationTrainer
        print("✅ ClassificationTrainer")
    except Exception as e:
        print(f"❌ ClassificationTrainer: {e}")
        return False
    
    try:
        from classification_recognizer import ClassificationRecognizer
        print("✅ ClassificationRecognizer")
    except Exception as e:
        print(f"❌ ClassificationRecognizer: {e}")
        return False
    
    try:
        from classification_gui_components import ClassificationTrainingPanel, ClassificationRecognitionPanel
        print("✅ GUI组件")
    except Exception as e:
        print(f"❌ GUI组件: {e}")
        return False
    
    return True

def test_initialization_order():
    """测试初始化顺序"""
    print("\n测试初始化顺序...")
    
    try:
        # 模拟GUI初始化过程
        from classification_manager import ClassificationManager
        from classification_trainer import ClassificationTrainer
        from classification_recognizer import ClassificationRecognizer
        
        # 1. 初始化分类管理器
        classification_manager = ClassificationManager()
        print("✅ 分类管理器初始化")
        
        # 2. 初始化分类训练器
        classification_trainer = ClassificationTrainer(classification_manager)
        print("✅ 分类训练器初始化")
        
        # 3. 初始化分类识别器（需要YOLO管理器）
        try:
            from yolo_manager import YOLOManager
            yolo_manager = YOLOManager()
            classification_recognizer = ClassificationRecognizer(yolo_manager, classification_manager)
            print("✅ 分类识别器初始化")
        except Exception as e:
            print(f"⚠️ 分类识别器初始化失败（YOLO不可用）: {e}")
            classification_recognizer = None
        
        # 4. 测试GUI组件创建
        try:
            from classification_gui_components import ClassificationTrainingPanel, ClassificationRecognitionPanel
            print("✅ GUI组件可以创建")
        except Exception as e:
            print(f"❌ GUI组件创建失败: {e}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 初始化测试失败: {e}")
        return False

def test_gui_creation():
    """测试GUI创建（不实际显示）"""
    print("\n测试GUI创建...")
    
    try:
        import tkinter as tk
        from tkinter import ttk
        
        # 创建测试窗口
        root = tk.Tk()
        root.withdraw()  # 隐藏窗口
        
        # 测试分类管理器
        from classification_manager import ClassificationManager
        from classification_trainer import ClassificationTrainer
        
        classification_manager = ClassificationManager()
        classification_trainer = ClassificationTrainer(classification_manager)
        
        # 创建测试框架
        test_frame = ttk.Frame(root)
        
        # 测试分类训练面板创建
        try:
            from classification_gui_components import ClassificationTrainingPanel
            training_panel = ClassificationTrainingPanel(test_frame, classification_manager, classification_trainer)
            print("✅ 分类训练面板创建成功")
        except Exception as e:
            print(f"❌ 分类训练面板创建失败: {e}")
            return False
        
        # 测试分类识别面板创建
        try:
            from classification_gui_components import ClassificationRecognitionPanel
            from yolo_manager import YOLOManager
            from classification_recognizer import ClassificationRecognizer
            
            yolo_manager = YOLOManager()
            classification_recognizer = ClassificationRecognizer(yolo_manager, classification_manager)
            
            recognition_panel = ClassificationRecognitionPanel(test_frame, classification_recognizer)
            print("✅ 分类识别面板创建成功")
        except Exception as e:
            print(f"⚠️ 分类识别面板创建失败（可能YOLO不可用）: {e}")
        
        # 清理
        root.destroy()
        
        return True
        
    except Exception as e:
        print(f"❌ GUI创建测试失败: {e}")
        return False

def check_main_gui_fixes():
    """检查主GUI修复"""
    print("\n检查主GUI修复...")
    
    try:
        with open('enhanced_segmentation_gui.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查初始化顺序
        create_widgets_pos = content.find('self.create_widgets()')
        initialize_systems_pos = content.find('self.initialize_systems()')
        
        if initialize_systems_pos < create_widgets_pos:
            print("✅ 初始化顺序正确（先初始化系统，再创建界面）")
        else:
            print("❌ 初始化顺序错误")
            return False
        
        # 检查变量初始化
        if 'self.classification_manager = None' in content:
            print("✅ 分类管理器变量已初始化")
        else:
            print("❌ 分类管理器变量未初始化")
            return False
        
        # 检查错误处理
        if 'hasattr(self, \'classification_manager\')' in content:
            print("✅ 添加了属性检查")
        else:
            print("❌ 缺少属性检查")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 检查主GUI失败: {e}")
        return False

def main():
    print("🔧 GUI初始化修复测试")
    print("=" * 40)
    
    tests = [
        ("导入测试", test_imports),
        ("初始化顺序", test_initialization_order),
        ("GUI创建", test_gui_creation),
        ("主GUI修复", check_main_gui_fixes),
    ]
    
    passed = 0
    for test_name, test_func in tests:
        print(f"\n{test_name}:")
        if test_func():
            passed += 1
    
    print(f"\n结果: {passed}/{len(tests)} 项测试通过")
    
    if passed == len(tests):
        print("🎉 GUI初始化修复成功！")
        print("\n✅ 修复内容:")
        print("- 调整了初始化顺序")
        print("- 添加了变量预初始化")
        print("- 改进了错误处理")
        print("- 增加了属性检查")
        print("\n🚀 现在可以正常启动GUI了！")
    else:
        print("⚠️ 部分问题仍需解决")

if __name__ == "__main__":
    main()
