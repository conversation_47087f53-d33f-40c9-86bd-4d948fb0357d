#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GPU-accelerated SAM Everything GUI (Fixed Version)
- GPU/CPU device selection
- YOLO label generation
- Enhanced performance monitoring
- Official SAM demo parameters
- Fixed tkinter Scale compatibility issues
"""

import os
import sys
import cv2
import numpy as np
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
from PIL import Image, ImageTk
import threading
import glob
import time

# Try to import the GPU-accelerated SAM Everything
try:
    from sam_everything_gpu import GPUAcceleratedSAMEverything
    GPU_AVAILABLE = True
except ImportError:
    print("Warning: GPU-accelerated SAM not available, falling back to original version")
    try:
        from sam_everything_simplified import SimplifiedSAMEverything as GPUAcceleratedSAMEverything
        GPU_AVAILABLE = False
    except ImportError:
        print("Error: Neither GPU nor original SAM version available")
        sys.exit(1)

class GPUAcceleratedSAMEverythingGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("GPU-accelerated SAM Everything - 对象提取工具")

        # 适配小屏幕的窗口设置
        self.setup_window_size()

        # 变量
        self.current_image = None
        self.current_image_path = None
        self.current_directory = None
        self.sam_everything = None
        self.result = None
        self.image_files = []

        # 批量处理进度变量
        self.batch_total_files = 0
        self.batch_current_file = 0
        self.batch_current_filename = ""

        # 创建界面
        self.create_widgets()

        # 检查GPU可用性
        self.check_gpu_availability()

        # 初始化SAM Everything
        self.initialize_sam_everything()

    def setup_window_size(self):
        """设置适配小屏幕的窗口大小"""
        # 获取屏幕尺寸
        screen_width = self.root.winfo_screenwidth()
        screen_height = self.root.winfo_screenheight()

        # 计算合适的窗口大小（占屏幕的85%）
        window_width = min(1400, int(screen_width * 0.85))
        window_height = min(900, int(screen_height * 0.85))

        # 设置窗口大小和位置（居中）
        x = (screen_width - window_width) // 2
        y = (screen_height - window_height) // 2

        self.root.geometry(f"{window_width}x{window_height}+{x}+{y}")

        # 设置最小窗口大小
        self.root.minsize(1200, 700)

        # 允许窗口调整大小
        self.root.resizable(True, True)
    
    def check_gpu_availability(self):
        """检查GPU可用性"""
        try:
            import torch
            self.gpu_available = torch.cuda.is_available()
            if self.gpu_available:
                gpu_name = torch.cuda.get_device_name()
                self.log_message(f"检测到GPU: {gpu_name}")
            else:
                self.log_message("未检测到可用GPU，将使用CPU")
        except ImportError:
            self.gpu_available = False
            self.log_message("PyTorch未安装，将使用CPU")
    
    def safe_imread_backup(self, image_path):
        """备用的安全图像读取方法"""
        try:
            # 方法1: 使用numpy和cv2.imdecode
            with open(image_path, 'rb') as f:
                image_data = f.read()
            
            nparr = np.frombuffer(image_data, np.uint8)
            image = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
            
            if image is not None:
                return image
            
            # 方法2: 使用PIL然后转换
            from PIL import Image as PILImage
            pil_image = PILImage.open(image_path)
            if pil_image.mode != 'RGB':
                pil_image = pil_image.convert('RGB')
            
            image_array = np.array(pil_image)
            image_bgr = cv2.cvtColor(image_array, cv2.COLOR_RGB2BGR)
            
            return image_bgr
            
        except Exception as e:
            self.log_message(f"读取图像失败 {image_path}: {e}")
            return None
    
    def create_widgets(self):
        """创建界面组件（带滚动条支持）"""
        # 主框架
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # 创建左侧滚动区域
        self.create_scrollable_left_panel(main_frame)

        # 右侧图像显示区域
        self.create_image_display(main_frame)

    def create_scrollable_left_panel(self, parent):
        """创建带滚动条的左侧控制面板"""
        # 左侧面板容器
        left_container = ttk.Frame(parent)
        left_container.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 5))

        # 创建Canvas和滚动条
        self.left_canvas = tk.Canvas(left_container, width=420, highlightthickness=0)
        left_scrollbar = ttk.Scrollbar(left_container, orient=tk.VERTICAL, command=self.left_canvas.yview)
        self.scrollable_left_frame = ttk.Frame(self.left_canvas)

        # 配置滚动
        self.scrollable_left_frame.bind(
            "<Configure>",
            lambda e: self.left_canvas.configure(scrollregion=self.left_canvas.bbox("all"))
        )

        self.left_canvas.create_window((0, 0), window=self.scrollable_left_frame, anchor="nw")
        self.left_canvas.configure(yscrollcommand=left_scrollbar.set)

        # 布局Canvas和滚动条
        self.left_canvas.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        left_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # 绑定鼠标滚轮事件
        self.bind_mousewheel(self.left_canvas)

        # 在滚动框架中创建所有控件
        self.create_all_controls(self.scrollable_left_frame)

    def bind_mousewheel(self, canvas):
        """绑定鼠标滚轮事件"""
        def _on_mousewheel(event):
            canvas.yview_scroll(int(-1*(event.delta/120)), "units")

        def _bind_to_mousewheel(event):
            canvas.bind_all("<MouseWheel>", _on_mousewheel)

        def _unbind_from_mousewheel(event):
            canvas.unbind_all("<MouseWheel>")

        canvas.bind('<Enter>', _bind_to_mousewheel)
        canvas.bind('<Leave>', _unbind_from_mousewheel)

    def create_all_controls(self, parent):
        """在滚动区域中创建所有控件"""
        # 设备选择区域
        self.create_device_selection(parent)

        # 文件管理区域
        self.create_file_management(parent)

        # 操作按钮区域（移到前面，确保重要按钮可见）
        self.create_action_buttons(parent)

        # 可折叠的参数控制区域
        self.create_collapsible_parameter_controls(parent)

        # YOLO设置区域
        self.create_yolo_settings(parent)

        # 改进的状态信息区域
        self.create_enhanced_status_area(parent)
    
    def create_device_selection(self, parent):
        """创建设备选择区域"""
        device_frame = ttk.LabelFrame(parent, text="设备选择")
        device_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # 设备选择
        self.device_var = tk.StringVar(value="auto")
        
        device_options = [
            ("自动选择", "auto"),
            ("强制使用GPU", "gpu"),
            ("强制使用CPU", "cpu")
        ]
        
        for text, value in device_options:
            ttk.Radiobutton(device_frame, text=text, variable=self.device_var, 
                           value=value, command=self.on_device_change).pack(anchor=tk.W, padx=5)
        
        # GPU状态显示
        self.gpu_status_label = ttk.Label(device_frame, text="检查GPU状态中...", 
                                         foreground="blue")
        self.gpu_status_label.pack(anchor=tk.W, padx=5, pady=2)
        
        # 性能监控
        perf_frame = ttk.Frame(device_frame)
        perf_frame.pack(fill=tk.X, padx=5, pady=2)
        
        ttk.Label(perf_frame, text="处理时间:").pack(side=tk.LEFT)
        self.processing_time_label = ttk.Label(perf_frame, text="--", foreground="green")
        self.processing_time_label.pack(side=tk.LEFT, padx=(5, 0))
    
    def create_file_management(self, parent):
        """创建文件管理区域"""
        file_frame = ttk.LabelFrame(parent, text="文件管理")
        file_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 文件夹选择
        folder_frame = ttk.Frame(file_frame)
        folder_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Button(folder_frame, text="选择文件夹", 
                  command=self.select_folder).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(folder_frame, text="选择单个文件", 
                  command=self.select_single_file).pack(side=tk.LEFT)
        
        # 当前文件夹显示
        self.folder_label = ttk.Label(file_frame, text="未选择文件夹", 
                                     wraplength=420, foreground="blue")
        self.folder_label.pack(fill=tk.X, padx=5, pady=2)
        
        # 图像文件列表
        list_frame = ttk.Frame(file_frame)
        list_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        ttk.Label(list_frame, text="图像文件列表:").pack(anchor=tk.W)
        
        # 创建列表框和滚动条
        list_container = ttk.Frame(list_frame)
        list_container.pack(fill=tk.BOTH, expand=True)
        
        self.file_listbox = tk.Listbox(list_container, height=6)
        scrollbar = ttk.Scrollbar(list_container, orient=tk.VERTICAL, 
                                 command=self.file_listbox.yview)
        self.file_listbox.configure(yscrollcommand=scrollbar.set)
        
        self.file_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 绑定列表选择事件
        self.file_listbox.bind('<<ListboxSelect>>', self.on_file_select)
        
        # 文件统计
        self.file_stats_label = ttk.Label(list_frame, text="文件数量: 0")
        self.file_stats_label.pack(anchor=tk.W, pady=2)
    
    def create_collapsible_parameter_controls(self, parent):
        """创建可折叠的参数控制区域"""
        # 主参数框架
        param_main_frame = ttk.Frame(parent)
        param_main_frame.pack(fill=tk.X, padx=5, pady=5)

        # 可折叠标题栏
        self.param_collapsed = tk.BooleanVar(value=True)  # 默认折叠
        param_header = ttk.Frame(param_main_frame)
        param_header.pack(fill=tk.X)

        # 折叠/展开按钮
        self.param_toggle_btn = ttk.Button(param_header, text="▶ 高级参数设置",
                                          command=self.toggle_parameters)
        self.param_toggle_btn.pack(side=tk.LEFT)

        # 参数内容框架（可折叠）
        self.param_content_frame = ttk.Frame(param_main_frame)

        # 创建参数控件
        self.create_parameter_widgets(self.param_content_frame)

    def toggle_parameters(self):
        """切换参数区域的显示/隐藏"""
        if self.param_collapsed.get():
            # 展开
            self.param_content_frame.pack(fill=tk.X, pady=(5, 0))
            self.param_toggle_btn.config(text="▼ 高级参数设置")
            self.param_collapsed.set(False)
        else:
            # 折叠
            self.param_content_frame.pack_forget()
            self.param_toggle_btn.config(text="▶ 高级参数设置")
            self.param_collapsed.set(True)

        # 更新滚动区域
        self.root.after(10, lambda: self.left_canvas.configure(scrollregion=self.left_canvas.bbox("all")))

    def create_parameter_widgets(self, parent):
        """创建参数控制组件"""
        # SAM参数组
        sam_frame = ttk.LabelFrame(parent, text="SAM参数 (官方Demo默认值)")
        sam_frame.pack(fill=tk.X, padx=5, pady=5)

        # 使用更紧凑的布局
        # 每边点数
        points_frame = ttk.Frame(sam_frame)
        points_frame.pack(fill=tk.X, padx=5, pady=2)
        ttk.Label(points_frame, text="每边点数:", width=12).pack(side=tk.LEFT)
        self.points_per_side = tk.IntVar(value=32)
        tk.Scale(points_frame, from_=16, to=64, variable=self.points_per_side,
                orient=tk.HORIZONTAL, length=250).pack(side=tk.LEFT, fill=tk.X, expand=True)

        # 预测IoU阈值
        iou_frame = ttk.Frame(sam_frame)
        iou_frame.pack(fill=tk.X, padx=5, pady=2)
        ttk.Label(iou_frame, text="IoU阈值:", width=12).pack(side=tk.LEFT)
        self.pred_iou_thresh = tk.DoubleVar(value=0.88)
        tk.Scale(iou_frame, from_=0.5, to=1.0, variable=self.pred_iou_thresh,
                orient=tk.HORIZONTAL, resolution=0.01, length=250).pack(side=tk.LEFT, fill=tk.X, expand=True)

        # 稳定性分数阈值
        stability_frame = ttk.Frame(sam_frame)
        stability_frame.pack(fill=tk.X, padx=5, pady=2)
        ttk.Label(stability_frame, text="稳定性阈值:", width=12).pack(side=tk.LEFT)
        self.stability_score_thresh = tk.DoubleVar(value=0.95)
        tk.Scale(stability_frame, from_=0.5, to=1.0, variable=self.stability_score_thresh,
                orient=tk.HORIZONTAL, resolution=0.01, length=250).pack(side=tk.LEFT, fill=tk.X, expand=True)

        # 最小对象面积
        area_frame = ttk.Frame(sam_frame)
        area_frame.pack(fill=tk.X, padx=5, pady=2)
        ttk.Label(area_frame, text="最小面积:", width=12).pack(side=tk.LEFT)
        self.min_object_area = tk.IntVar(value=100)
        tk.Scale(area_frame, from_=50, to=1000, variable=self.min_object_area,
                orient=tk.HORIZONTAL, length=250).pack(side=tk.LEFT, fill=tk.X, expand=True)

        # 重叠过滤阈值
        overlap_frame = ttk.Frame(sam_frame)
        overlap_frame.pack(fill=tk.X, padx=5, pady=2)
        ttk.Label(overlap_frame, text="重叠阈值:", width=12).pack(side=tk.LEFT)
        self.overlap_threshold = tk.DoubleVar(value=0.1)
        tk.Scale(overlap_frame, from_=0.0, to=0.5, variable=self.overlap_threshold,
                orient=tk.HORIZONTAL, resolution=0.01, length=250).pack(side=tk.LEFT, fill=tk.X, expand=True)

        # 最大掩膜比例阈值
        max_ratio_frame = ttk.Frame(sam_frame)
        max_ratio_frame.pack(fill=tk.X, padx=5, pady=2)
        ttk.Label(max_ratio_frame, text="最大面积比:", width=12).pack(side=tk.LEFT)
        self.max_mask_ratio = tk.DoubleVar(value=0.8)
        tk.Scale(max_ratio_frame, from_=0.5, to=1.0, variable=self.max_mask_ratio,
                orient=tk.HORIZONTAL, resolution=0.01, length=250).pack(side=tk.LEFT, fill=tk.X, expand=True)

        # 可视化选项
        vis_frame = ttk.LabelFrame(parent, text="可视化选项")
        vis_frame.pack(fill=tk.X, padx=5, pady=5)

        # 显示掩膜选项
        mask_frame = ttk.Frame(vis_frame)
        mask_frame.pack(fill=tk.X, padx=5, pady=2)
        self.show_masks = tk.BooleanVar(value=True)
        ttk.Checkbutton(mask_frame, text="显示掩膜", variable=self.show_masks).pack(side=tk.LEFT)

        # 掩膜提取选项
        extraction_frame = ttk.Frame(vis_frame)
        extraction_frame.pack(fill=tk.X, padx=5, pady=2)
        self.use_mask_extraction = tk.BooleanVar(value=True)
        ttk.Checkbutton(extraction_frame, text="掩膜提取 (透明背景)", variable=self.use_mask_extraction).pack(side=tk.LEFT)

        # 掩膜透明度
        alpha_frame = ttk.Frame(vis_frame)
        alpha_frame.pack(fill=tk.X, padx=5, pady=2)
        ttk.Label(alpha_frame, text="掩膜透明度:", width=12).pack(side=tk.LEFT)
        self.mask_alpha = tk.DoubleVar(value=0.35)
        tk.Scale(alpha_frame, from_=0.1, to=0.8, variable=self.mask_alpha,
                orient=tk.HORIZONTAL, resolution=0.01, length=250).pack(side=tk.LEFT, fill=tk.X, expand=True)
    
    def create_yolo_settings(self, parent):
        """创建YOLO设置区域"""
        yolo_frame = ttk.LabelFrame(parent, text="YOLO标签设置")
        yolo_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # YOLO标签生成选项
        self.generate_yolo_labels = tk.BooleanVar(value=True)
        ttk.Checkbutton(yolo_frame, text="生成YOLO标签文件", 
                       variable=self.generate_yolo_labels).pack(anchor=tk.W)
        
        # 默认类别ID
        class_frame = ttk.Frame(yolo_frame)
        class_frame.pack(fill=tk.X, pady=2)
        
        ttk.Label(class_frame, text="默认类别ID:").pack(side=tk.LEFT)
        self.default_class_id = tk.IntVar(value=0)
        ttk.Spinbox(class_frame, from_=0, to=999, textvariable=self.default_class_id, 
                   width=10).pack(side=tk.LEFT, padx=(5, 0))
        
        # YOLO标签文件夹状态
        self.yolo_status_label = ttk.Label(yolo_frame, text="YOLO标签将保存到: yolo_label/",
                                          foreground="green")
        self.yolo_status_label.pack(anchor=tk.W, pady=2)

    def create_action_buttons(self, parent):
        """创建操作按钮区域"""
        button_frame = ttk.LabelFrame(parent, text="操作")
        button_frame.pack(fill=tk.X, padx=5, pady=5)

        # 单个处理
        self.process_button = ttk.Button(button_frame, text="处理当前图像",
                                       command=self.process_current_image,
                                       state=tk.DISABLED)
        self.process_button.pack(fill=tk.X, pady=2)

        # 批量处理
        self.batch_button = ttk.Button(button_frame, text="批量处理所有图像",
                                     command=self.batch_process_images,
                                     state=tk.DISABLED)
        self.batch_button.pack(fill=tk.X, pady=2)

        # 输出设置
        output_frame = ttk.Frame(button_frame)
        output_frame.pack(fill=tk.X, pady=5)

        ttk.Label(output_frame, text="输出文件夹:").pack(anchor=tk.W)
        self.output_dir_var = tk.StringVar(value="output")
        ttk.Entry(output_frame, textvariable=self.output_dir_var).pack(fill=tk.X, pady=2)

        ttk.Button(output_frame, text="选择输出文件夹",
                  command=self.select_output_folder).pack(fill=tk.X, pady=2)

    def create_enhanced_status_area(self, parent):
        """创建改进的状态信息区域"""
        status_frame = ttk.LabelFrame(parent, text="状态信息")
        status_frame.pack(fill=tk.X, padx=5, pady=5)

        # 当前状态显示
        status_info_frame = ttk.Frame(status_frame)
        status_info_frame.pack(fill=tk.X, padx=5, pady=2)

        self.progress_var = tk.StringVar(value="就绪")
        ttk.Label(status_info_frame, textvariable=self.progress_var, font=('Arial', 9, 'bold')).pack(anchor=tk.W)

        # 当前处理文件信息
        self.current_file_var = tk.StringVar(value="")
        self.current_file_label = ttk.Label(status_info_frame, textvariable=self.current_file_var,
                                           font=('Arial', 8), foreground='blue')
        self.current_file_label.pack(anchor=tk.W)

        # 单个文件进度条
        progress_frame = ttk.Frame(status_frame)
        progress_frame.pack(fill=tk.X, padx=5, pady=2)

        ttk.Label(progress_frame, text="当前进度:", font=('Arial', 8)).pack(side=tk.LEFT)
        self.progress_bar = ttk.Progressbar(progress_frame, mode='indeterminate', length=200)
        self.progress_bar.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(5, 0))

        # 百分比显示
        self.progress_percent_var = tk.StringVar(value="")
        self.progress_percent_label = ttk.Label(progress_frame, textvariable=self.progress_percent_var,
                                               font=('Arial', 8), width=8)
        self.progress_percent_label.pack(side=tk.RIGHT)

        # 批量处理总体进度
        batch_progress_frame = ttk.Frame(status_frame)
        batch_progress_frame.pack(fill=tk.X, padx=5, pady=2)

        ttk.Label(batch_progress_frame, text="总体进度:", font=('Arial', 8)).pack(side=tk.LEFT)
        self.batch_progress_bar = ttk.Progressbar(batch_progress_frame, mode='determinate', length=200)
        self.batch_progress_bar.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(5, 0))

        # 批量进度百分比
        self.batch_progress_var = tk.StringVar(value="")
        self.batch_progress_label = ttk.Label(batch_progress_frame, textvariable=self.batch_progress_var,
                                             font=('Arial', 8), width=8)
        self.batch_progress_label.pack(side=tk.RIGHT)

        # 默认隐藏批量进度
        batch_progress_frame.pack_forget()
        self.batch_progress_frame = batch_progress_frame

        # 处理时间和统计信息
        stats_info_frame = ttk.Frame(status_frame)
        stats_info_frame.pack(fill=tk.X, padx=5, pady=2)

        ttk.Label(stats_info_frame, text="处理时间:", font=('Arial', 8)).pack(side=tk.LEFT)
        self.processing_time_label = ttk.Label(stats_info_frame, text="--",
                                              font=('Arial', 8), foreground="green")
        self.processing_time_label.pack(side=tk.LEFT, padx=(5, 15))

        ttk.Label(stats_info_frame, text="检测对象:", font=('Arial', 8)).pack(side=tk.LEFT)
        self.objects_count_label = ttk.Label(stats_info_frame, text="--",
                                            font=('Arial', 8), foreground="blue")
        self.objects_count_label.pack(side=tk.LEFT, padx=(5, 0))

        # 日志信息区域（更紧凑）
        log_frame = ttk.Frame(status_frame)
        log_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=2)

        ttk.Label(log_frame, text="处理日志:", font=('Arial', 8)).pack(anchor=tk.W)

        log_container = ttk.Frame(log_frame)
        log_container.pack(fill=tk.BOTH, expand=True)

        self.stats_text = tk.Text(log_container, height=3, wrap=tk.WORD, font=('Arial', 8))
        stats_scrollbar = ttk.Scrollbar(log_container, orient=tk.VERTICAL,
                                       command=self.stats_text.yview)
        self.stats_text.configure(yscrollcommand=stats_scrollbar.set)

        self.stats_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        stats_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

    def create_image_display(self, parent):
        """创建图像显示区域（适配小屏幕）"""
        image_frame = ttk.LabelFrame(parent, text="图像显示")
        image_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)

        # 图像信息标签（移到顶部）
        info_frame = ttk.Frame(image_frame)
        info_frame.pack(fill=tk.X, padx=5, pady=2)

        self.image_info_label = ttk.Label(info_frame, text="未加载图像", font=('Arial', 9, 'bold'))
        self.image_info_label.pack(side=tk.LEFT)

        # 缩放控制按钮
        zoom_frame = ttk.Frame(info_frame)
        zoom_frame.pack(side=tk.RIGHT)

        ttk.Button(zoom_frame, text="适应窗口", command=self.fit_image_to_window, width=8).pack(side=tk.LEFT, padx=2)
        ttk.Button(zoom_frame, text="原始大小", command=self.show_original_size, width=8).pack(side=tk.LEFT)

        # 创建Canvas用于显示图像（自适应大小）
        canvas_frame = ttk.Frame(image_frame)
        canvas_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        self.canvas = tk.Canvas(canvas_frame, bg='white')

        # 添加滚动条到图像显示区域
        h_scrollbar = ttk.Scrollbar(canvas_frame, orient=tk.HORIZONTAL, command=self.canvas.xview)
        v_scrollbar = ttk.Scrollbar(canvas_frame, orient=tk.VERTICAL, command=self.canvas.yview)

        self.canvas.configure(xscrollcommand=h_scrollbar.set, yscrollcommand=v_scrollbar.set)

        # 布局Canvas和滚动条
        self.canvas.grid(row=0, column=0, sticky="nsew")
        h_scrollbar.grid(row=1, column=0, sticky="ew")
        v_scrollbar.grid(row=0, column=1, sticky="ns")

        canvas_frame.grid_rowconfigure(0, weight=1)
        canvas_frame.grid_columnconfigure(0, weight=1)

        # 绑定Canvas调整大小事件
        self.canvas.bind('<Configure>', self.on_canvas_configure)

        # 存储当前显示的图像信息
        self.current_display_image = None
        self.image_scale_factor = 1.0
        self.fit_to_window = True

    def on_canvas_configure(self, event):
        """Canvas大小改变时的处理"""
        if self.current_display_image is not None and self.fit_to_window:
            self.fit_image_to_window()

    def fit_image_to_window(self):
        """将图像适应到窗口大小"""
        if self.current_display_image is not None:
            self.fit_to_window = True
            self.display_image_on_canvas(self.current_display_image)

    def show_original_size(self):
        """显示图像原始大小"""
        if self.current_display_image is not None:
            self.fit_to_window = False
            self.image_scale_factor = 1.0
            self.display_image_on_canvas(self.current_display_image)

    def log_message(self, message):
        """添加日志消息"""
        self.stats_text.insert(tk.END, f"{message}\n")
        self.stats_text.see(tk.END)
        self.root.update()

    def on_device_change(self):
        """设备选择改变事件"""
        device = self.device_var.get()
        self.log_message(f"设备选择改变为: {device}")

        # 重新初始化SAM Everything
        if self.sam_everything is not None:
            self.initialize_sam_everything()

    def initialize_sam_everything(self):
        """初始化SAM Everything"""
        self.log_message("正在初始化GPU-accelerated SAM Everything...")

        # 检查模型文件
        model_path = "sam_vit_h_4b8939.pth"
        if not os.path.exists(model_path):
            self.log_message(f"错误: SAM模型文件不存在: {model_path}")
            messagebox.showerror("错误", f"SAM模型文件不存在: {model_path}\n请确保模型文件在当前目录中")
            return

        try:
            config = self.get_current_config()
            self.sam_everything = GPUAcceleratedSAMEverything(config)

            if hasattr(self.sam_everything, 'sam_generator') and self.sam_everything.sam_generator is None:
                self.log_message("错误: SAM Everything初始化失败")
                messagebox.showerror("错误", "SAM Everything初始化失败")
                return

            self.log_message("GPU-accelerated SAM Everything初始化成功!")

            # 更新GPU状态显示
            device = config.get('device', 'auto')
            if GPU_AVAILABLE and (device == 'gpu' or (device == 'auto' and self.gpu_available)):
                self.gpu_status_label.config(text="当前使用: GPU", foreground="green")
            else:
                self.gpu_status_label.config(text="当前使用: CPU", foreground="orange")

        except Exception as e:
            self.log_message(f"初始化失败: {e}")
            messagebox.showerror("错误", f"初始化失败: {e}")

    def get_current_config(self):
        """获取当前配置"""
        config = {
            'checkpoint_path': 'sam_vit_h_4b8939.pth',
            'points_per_side': self.points_per_side.get(),
            'min_object_area': self.min_object_area.get(),
            'show_masks': self.show_masks.get(),
            'mask_alpha': self.mask_alpha.get(),
        }

        # 只有GPU版本才支持这些参数
        if GPU_AVAILABLE:
            config.update({
                'device': self.device_var.get(),
                'pred_iou_thresh': self.pred_iou_thresh.get(),
                'stability_score_thresh': self.stability_score_thresh.get(),
                'overlap_threshold': self.overlap_threshold.get(),
                'max_mask_ratio': self.max_mask_ratio.get(),
                'use_mask_extraction': self.use_mask_extraction.get(),
                'generate_yolo_labels': self.generate_yolo_labels.get(),
                'default_class_id': self.default_class_id.get(),
            })

        return config

    def select_folder(self):
        """选择文件夹"""
        folder_path = filedialog.askdirectory(title="选择包含图像的文件夹")

        if folder_path:
            self.current_directory = folder_path
            self.folder_label.config(text=f"当前文件夹: {os.path.basename(folder_path)}")

            # 扫描图像文件
            self.scan_image_files()

            # 启用批量处理按钮
            if self.sam_everything is not None and self.image_files:
                self.batch_button.config(state=tk.NORMAL)

            self.log_message(f"已选择文件夹: {folder_path}")

    def select_single_file(self):
        """选择单个文件"""
        file_path = filedialog.askopenfilename(
            title="选择图像文件",
            filetypes=[
                ("图像文件", "*.jpg *.jpeg *.png *.bmp *.tiff"),
                ("所有文件", "*.*")
            ]
        )

        if file_path:
            # 设置文件夹为文件所在目录
            self.current_directory = os.path.dirname(file_path)
            self.folder_label.config(text=f"当前文件夹: {os.path.basename(self.current_directory)}")

            # 扫描图像文件
            self.scan_image_files()

            # 选中当前文件
            filename = os.path.basename(file_path)
            try:
                index = self.image_files.index(filename)
                self.file_listbox.selection_set(index)
                self.load_selected_image()
            except ValueError:
                pass

            self.log_message(f"已选择文件: {filename}")

    def scan_image_files(self):
        """扫描当前目录中的图像文件"""
        if not self.current_directory:
            return

        # 支持的图像格式
        extensions = ['*.jpg', '*.jpeg', '*.png', '*.bmp', '*.tiff', '*.gif']

        self.image_files = []
        for ext in extensions:
            pattern = os.path.join(self.current_directory, ext)
            self.image_files.extend([os.path.basename(f) for f in glob.glob(pattern)])
            # 也搜索大写扩展名
            pattern_upper = os.path.join(self.current_directory, ext.upper())
            self.image_files.extend([os.path.basename(f) for f in glob.glob(pattern_upper)])

        # 去重并排序
        self.image_files = sorted(list(set(self.image_files)))

        # 更新列表框
        self.file_listbox.delete(0, tk.END)
        for filename in self.image_files:
            self.file_listbox.insert(tk.END, filename)

        # 更新统计
        self.file_stats_label.config(text=f"文件数量: {len(self.image_files)}")

        self.log_message(f"扫描到 {len(self.image_files)} 个图像文件")

    def on_file_select(self, event):
        """文件列表选择事件"""
        selection = self.file_listbox.curselection()
        if selection:
            self.load_selected_image()

    def load_selected_image(self):
        """加载选中的图像"""
        selection = self.file_listbox.curselection()
        if not selection or not self.current_directory:
            return

        filename = self.image_files[selection[0]]
        image_path = os.path.join(self.current_directory, filename)

        try:
            # 使用安全方法加载图像
            if self.sam_everything and hasattr(self.sam_everything, 'safe_imread'):
                self.current_image = self.sam_everything.safe_imread(image_path)
            else:
                # 如果SAM Everything未初始化，使用备用方法
                self.current_image = self.safe_imread_backup(image_path)

            if self.current_image is None:
                raise ValueError("无法加载图像")

            self.current_image_path = image_path

            # 转换为RGB用于显示
            image_rgb = cv2.cvtColor(self.current_image, cv2.COLOR_BGR2RGB)

            # 显示图像
            self.display_image_on_canvas(image_rgb)

            # 更新图像信息
            h, w = self.current_image.shape[:2]
            self.image_info_label.config(text=f"{filename} - {w}x{h}")

            # 启用处理按钮
            if self.sam_everything is not None:
                self.process_button.config(state=tk.NORMAL)

            self.log_message(f"已加载图像: {filename}")

        except Exception as e:
            self.log_message(f"加载图像失败: {e}")
            messagebox.showerror("错误", f"加载图像失败: {e}")

    def display_image_on_canvas(self, image_array):
        """在Canvas上显示图像（支持缩放和滚动）"""
        # 存储当前图像
        self.current_display_image = image_array.copy()

        # 获取Canvas尺寸
        self.canvas.update()
        canvas_width = self.canvas.winfo_width()
        canvas_height = self.canvas.winfo_height()

        if canvas_width <= 1 or canvas_height <= 1:
            self.root.after(100, lambda: self.display_image_on_canvas(image_array))
            return

        # 获取图像尺寸
        h, w = image_array.shape[:2]

        # 计算缩放比例
        if self.fit_to_window:
            # 适应窗口大小
            scale = min(canvas_width / w, canvas_height / h) * 0.95
            self.image_scale_factor = scale
        else:
            # 使用当前缩放因子
            scale = self.image_scale_factor

        new_w = int(w * scale)
        new_h = int(h * scale)

        # 调整图像大小
        if scale != 1.0:
            resized_image = cv2.resize(image_array, (new_w, new_h))
        else:
            resized_image = image_array

        # 转换为PIL图像
        pil_image = Image.fromarray(resized_image)
        self.photo = ImageTk.PhotoImage(pil_image)

        # 清除Canvas并显示图像
        self.canvas.delete("all")

        # 如果图像小于Canvas，居中显示
        if new_w <= canvas_width and new_h <= canvas_height:
            x = (canvas_width - new_w) // 2
            y = (canvas_height - new_h) // 2
            self.canvas.create_image(x, y, anchor=tk.NW, image=self.photo)
            # 重置滚动区域
            self.canvas.configure(scrollregion=(0, 0, canvas_width, canvas_height))
        else:
            # 图像大于Canvas，支持滚动
            self.canvas.create_image(0, 0, anchor=tk.NW, image=self.photo)
            # 设置滚动区域
            self.canvas.configure(scrollregion=(0, 0, new_w, new_h))

    def select_output_folder(self):
        """选择输出文件夹"""
        folder_path = filedialog.askdirectory(title="选择输出文件夹")
        if folder_path:
            self.output_dir_var.set(folder_path)
            self.log_message(f"输出文件夹: {folder_path}")

    def process_current_image(self):
        """处理当前图像"""
        if self.current_image is None or self.sam_everything is None:
            return

        self.progress_var.set("正在处理...")
        self.progress_bar.start()
        self.process_button.config(state=tk.DISABLED)

        # 在新线程中处理
        def process_thread():
            try:
                # 更新配置
                config = self.get_current_config()
                if hasattr(self.sam_everything, 'config'):
                    self.sam_everything.config.update(config)

                # 创建输出目录
                base_name = os.path.splitext(os.path.basename(self.current_image_path))[0]
                output_dir = os.path.join(self.output_dir_var.get(), base_name)

                # 记录开始时间
                start_time = time.time()

                # 执行处理
                result = self.sam_everything.process_image(
                    self.current_image, self.current_image_path, output_dir)

                # 记录处理时间
                processing_time = time.time() - start_time

                # 在主线程中更新界面
                self.root.after(0, lambda: self.process_complete(result, processing_time))

            except Exception as e:
                self.root.after(0, lambda: self.process_error(str(e)))

        threading.Thread(target=process_thread, daemon=True).start()

    def update_progress(self, message, percent=None, current_file=None):
        """更新进度显示"""
        self.progress_var.set(message)

        if current_file:
            self.current_file_var.set(f"处理文件: {current_file}")

        if percent is not None:
            self.progress_percent_var.set(f"{percent:.1f}%")
            if hasattr(self, 'progress_bar') and self.progress_bar['mode'] == 'determinate':
                self.progress_bar['value'] = percent

    def update_batch_progress(self, current, total, message=""):
        """更新批量处理进度"""
        if total > 1:
            # 显示批量进度条
            self.batch_progress_frame.pack(fill=tk.X, padx=5, pady=2)

            percent = (current / total) * 100
            self.batch_progress_bar['value'] = percent
            self.batch_progress_var.set(f"{current}/{total}")

            if message:
                self.progress_var.set(f"{message} ({current}/{total})")
        else:
            # 隐藏批量进度条
            self.batch_progress_frame.pack_forget()

    def reset_progress(self):
        """重置进度显示"""
        self.progress_bar.stop()
        self.progress_bar.config(mode='indeterminate')
        self.progress_percent_var.set("")
        self.current_file_var.set("")
        self.batch_progress_frame.pack_forget()
        self.batch_progress_var.set("")

    def process_complete(self, result, processing_time):
        """处理完成"""
        self.reset_progress()
        self.progress_var.set("处理完成")
        self.process_button.config(state=tk.NORMAL)

        # 更新处理时间和对象数量显示
        self.processing_time_label.config(text=f"{processing_time:.2f}秒")

        if not result['success']:
            self.objects_count_label.config(text="0")
            self.log_message(f"处理失败: {result.get('error', '未知错误')}")
            return

        # 更新统计信息
        self.objects_count_label.config(text=str(result['objects_count']))

        # 显示结果
        self.log_message(f"✅ 处理完成! 用时: {result['processing_time']:.2f}秒")
        self.log_message(f"🎯 最终对象: {result['objects_count']} 个")

        # 显示过滤统计信息
        if result.get('original_masks_count') and result.get('filtered_masks_count'):
            original_count = result['original_masks_count']
            filtered_count = result['filtered_masks_count']
            whole_image_removed = result.get('whole_image_removed_count', 0)
            size_filtered_count = result.get('size_filtered_count', filtered_count)

            # 计算各阶段移除的数量
            overlap_removed = size_filtered_count - filtered_count
            size_removed = original_count - whole_image_removed - size_filtered_count

            self.log_message(f"🔍 掩膜过滤统计:")
            if whole_image_removed > 0:
                self.log_message(f"   📏 全图掩膜: 移除 {whole_image_removed} 个")
            if size_removed > 0:
                self.log_message(f"   📐 小面积: 移除 {size_removed} 个")
            if overlap_removed > 0:
                self.log_message(f"   🔄 重叠掩膜: 移除 {overlap_removed} 个")
            self.log_message(f"   📊 总计: {original_count} → {filtered_count}")

        self.log_message(f"📁 结果保存到: {result['output_dir']}")

        # 显示YOLO标签信息
        if result.get('yolo_label_file'):
            self.log_message(f"🏷️ YOLO标签: {os.path.basename(result['yolo_label_file'])}")

        # 显示提取方法信息
        if result.get('objects') and len(result['objects']) > 0:
            first_object = result['objects'][0]
            if first_object.get('has_transparency'):
                self.log_message(f"🖼️ 对象提取: PNG格式 (透明背景)")
            else:
                self.log_message(f"🖼️ 对象提取: JPG格式 (矩形裁剪)")

        # 显示结果图像
        if 'result_image' in result:
            result_rgb = cv2.cvtColor(result['result_image'], cv2.COLOR_BGR2RGB)
            self.display_image_on_canvas(result_rgb)

    def process_error(self, error_msg):
        """处理错误"""
        self.reset_progress()
        self.progress_var.set("处理失败")
        self.process_button.config(state=tk.NORMAL)
        self.processing_time_label.config(text="--")
        self.objects_count_label.config(text="--")
        self.log_message(f"❌ 处理失败: {error_msg}")
        messagebox.showerror("错误", f"处理失败: {error_msg}")

    def batch_process_images(self):
        """批量处理图像"""
        if not self.image_files or self.sam_everything is None:
            return

        # 确认批量处理
        if not messagebox.askyesno("确认", f"将处理 {len(self.image_files)} 个图像文件，是否继续？"):
            return

        self.progress_var.set("批量处理中...")
        self.progress_bar.start()
        self.batch_button.config(state=tk.DISABLED)

        # 在新线程中处理
        def batch_process_thread():
            try:
                # 更新配置
                config = self.get_current_config()
                if hasattr(self.sam_everything, 'config'):
                    self.sam_everything.config.update(config)

                # 记录开始时间
                start_time = time.time()

                # 设置批量处理变量
                self.batch_total_files = len(self.image_files)
                self.batch_current_file = 0

                # 初始化批量进度
                self.root.after(0, lambda: self.update_batch_progress(0, self.batch_total_files, "开始批量处理"))

                # 如果有自定义批量处理方法，使用它
                if hasattr(self.sam_everything, 'batch_process_with_progress'):
                    result = self.sam_everything.batch_process_with_progress(
                        self.current_directory, self.output_dir_var.get(),
                        progress_callback=self.batch_progress_callback)
                else:
                    # 使用原有的批量处理方法
                    result = self.sam_everything.batch_process(
                        self.current_directory, self.output_dir_var.get())

                # 记录处理时间
                processing_time = time.time() - start_time

                # 在主线程中更新界面
                self.root.after(0, lambda: self.batch_complete(result, processing_time))

            except Exception as e:
                self.root.after(0, lambda: self.batch_error(str(e)))

        def batch_progress_callback(current, total, filename):
            """批量处理进度回调"""
            self.batch_current_file = current
            self.batch_current_filename = filename
            self.root.after(0, lambda: self.update_batch_progress(
                current, total, f"处理中: {filename}"))
            self.root.after(0, lambda: self.update_progress(
                f"批量处理中 ({current}/{total})",
                current_file=filename))

        threading.Thread(target=batch_process_thread, daemon=True).start()

    def batch_complete(self, result, processing_time):
        """批量处理完成"""
        self.reset_progress()
        self.progress_var.set("批量处理完成")
        self.batch_button.config(state=tk.NORMAL)

        # 更新处理时间和统计显示
        self.processing_time_label.config(text=f"{processing_time:.2f}秒")

        if not result['success']:
            self.objects_count_label.config(text="--")
            self.log_message(f"❌ 批量处理失败: {result.get('error', '未知错误')}")
            return

        # 更新统计信息
        self.objects_count_label.config(text=str(result.get('total_objects', 0)))

        # 显示结果
        self.log_message(f"🎉 批量处理完成!")
        self.log_message(f"📊 处理文件: {result['processed_count']}/{result['total_files']}")
        self.log_message(f"🎯 总对象数: {result['total_objects']}")
        self.log_message(f"📁 结果保存到: {self.output_dir_var.get()}")
        if GPU_AVAILABLE:
            self.log_message(f"🏷️ YOLO标签已生成到各自的yolo_label文件夹")

        # 计算平均处理时间
        avg_time = processing_time / result['total_files'] if result['total_files'] > 0 else 0
        self.log_message(f"⏱️ 平均处理时间: {avg_time:.2f}秒/图像")

        messagebox.showinfo("批量处理完成",
                           f"🎉 批量处理完成!\n\n"
                           f"📊 处理文件: {result['processed_count']}/{result['total_files']}\n"
                           f"🎯 总对象数: {result['total_objects']}\n"
                           f"⏱️ 总处理时间: {processing_time:.2f}秒\n"
                           f"📈 平均速度: {avg_time:.2f}秒/图像")

    def batch_error(self, error_msg):
        """批量处理错误"""
        self.reset_progress()
        self.progress_var.set("批量处理失败")
        self.batch_button.config(state=tk.NORMAL)
        self.processing_time_label.config(text="--")
        self.objects_count_label.config(text="--")
        self.log_message(f"❌ 批量处理失败: {error_msg}")
        messagebox.showerror("错误", f"批量处理失败: {error_msg}")

def main():
    root = tk.Tk()
    app = GPUAcceleratedSAMEverythingGUI(root)
    root.mainloop()

if __name__ == "__main__":
    main()
