#!/usr/bin/env python3
"""
调试GUI启动问题
"""

import sys
import traceback

def test_gui_import():
    """测试GUI导入"""
    try:
        print("测试导入tkinter...")
        import tkinter as tk
        from tkinter import ttk
        print("✅ tkinter导入成功")
        
        print("测试导入主GUI类...")
        from enhanced_segmentation_gui import EnhancedSegmentationGUI
        print("✅ 主GUI类导入成功")
        
        print("创建根窗口...")
        root = tk.Tk()
        root.withdraw()  # 隐藏窗口
        print("✅ 根窗口创建成功")
        
        print("创建GUI实例...")
        app = EnhancedSegmentationGUI(root)
        print("✅ GUI实例创建成功")
        
        print("清理...")
        root.destroy()
        print("✅ 测试完成")
        
        return True
        
    except Exception as e:
        print(f"❌ 错误: {e}")
        print("详细错误信息:")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_gui_import()
    if success:
        print("🎉 GUI可以正常创建！")
    else:
        print("💥 GUI创建失败")
    sys.exit(0 if success else 1)
