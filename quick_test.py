#!/usr/bin/env python3
"""
快速测试分类系统
"""

print("开始测试...")

# 测试1: 检查文件
import os
files = ['classification_manager.py', 'classification_trainer.py', 'classification_recognizer.py', 'classification_gui_components.py']
for f in files:
    if os.path.exists(f):
        print(f"✅ {f}")
    else:
        print(f"❌ {f}")

# 测试2: 检查主GUI修改
try:
    with open('enhanced_segmentation_gui.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    if 'classification_manager' in content:
        print("✅ 主GUI包含分类管理器")
    else:
        print("❌ 主GUI缺少分类管理器")
    
    if 'create_classification_training_interface' in content:
        print("✅ 主GUI包含分类训练界面")
    else:
        print("❌ 主GUI缺少分类训练界面")
        
except Exception as e:
    print(f"❌ 检查主GUI失败: {e}")

# 测试3: 简单导入测试
try:
    from classification_manager import ClassificationManager
    print("✅ 可以导入ClassificationManager")
except Exception as e:
    print(f"❌ 导入ClassificationManager失败: {e}")

try:
    from classification_trainer import ClassificationTrainer
    print("✅ 可以导入ClassificationTrainer")
except Exception as e:
    print(f"❌ 导入ClassificationTrainer失败: {e}")

try:
    from classification_recognizer import ClassificationRecognizer
    print("✅ 可以导入ClassificationRecognizer")
except Exception as e:
    print(f"❌ 导入ClassificationRecognizer失败: {e}")

try:
    from classification_gui_components import ClassificationTrainingPanel
    print("✅ 可以导入GUI组件")
except Exception as e:
    print(f"❌ 导入GUI组件失败: {e}")

print("测试完成！")
