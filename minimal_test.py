#!/usr/bin/env python3
"""
最小化测试
"""

print("开始最小化测试...")

try:
    print("1. 测试轻量级分类管理器...")
    from lightweight_classification_manager import LightweightClassificationManager
    manager = LightweightClassificationManager()
    print("✅ 轻量级分类管理器导入成功")
    
    print("2. 测试tkinter...")
    import tkinter as tk
    root = tk.Tk()
    root.withdraw()
    print("✅ tkinter工作正常")
    
    print("3. 测试主GUI类导入...")
    # 只导入类，不创建实例
    import sys
    import importlib.util
    
    spec = importlib.util.spec_from_file_location("enhanced_segmentation_gui", "enhanced_segmentation_gui.py")
    module = importlib.util.module_from_spec(spec)
    
    print("✅ 主GUI模块可以加载")
    
    root.destroy()
    print("🎉 基本测试通过！")
    
except Exception as e:
    print(f"❌ 错误: {e}")
    import traceback
    traceback.print_exc()

print("测试完成")
