#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版YOLO数据集修复工具
解决Windows编码问题，专注于核心功能
"""

import os
import sys
import json
import shutil
from pathlib import Path

def setup_console_encoding():
    """设置控制台编码"""
    if sys.platform == "win32":
        try:
            # 设置控制台为UTF-8编码
            os.system("chcp 65001 >nul 2>&1")
        except:
            pass

def find_json_files(base_dir):
    """查找YOLO JSON标注文件"""
    print(f"[查找] 在 {base_dir} 中查找分割结果...")
    
    json_files = []
    for root, dirs, files in os.walk(base_dir):
        for file in files:
            if file.endswith('_yolo_annotations.json'):
                json_path = os.path.join(root, file)
                
                # 查找对应的图像文件
                base_name = file.replace('_yolo_annotations.json', '')
                image_extensions = ['.jpg', '.jpeg', '.png', '.bmp']
                
                image_path = None
                for ext in image_extensions:
                    potential_image = os.path.join(root, f"{base_name}{ext}")
                    if os.path.exists(potential_image):
                        image_path = potential_image
                        break
                
                if image_path:
                    json_files.append({
                        'json_path': json_path,
                        'image_path': image_path,
                        'base_name': base_name
                    })
                    print(f"  [找到] {base_name}")
    
    print(f"[统计] 总共找到 {len(json_files)} 个标注文件")
    return json_files

def convert_json_to_yolo(json_path, output_txt):
    """将JSON转换为YOLO格式"""
    try:
        with open(json_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        image_width = data.get('imageWidth', 640)
        image_height = data.get('imageHeight', 640)
        
        with open(output_txt, 'w') as f:
            shapes = data.get('shapes', [])
            for shape in shapes:
                if shape.get('label') == 'seed' and shape.get('shape_type') == 'polygon':
                    points = shape.get('points', [])
                    if len(points) >= 4:
                        # 计算边界框
                        x_coords = [p[0] for p in points]
                        y_coords = [p[1] for p in points]
                        
                        x_min, x_max = min(x_coords), max(x_coords)
                        y_min, y_max = min(y_coords), max(y_coords)
                        
                        # 转换为YOLO格式
                        center_x = (x_min + x_max) / 2.0 / image_width
                        center_y = (y_min + y_max) / 2.0 / image_height
                        width = (x_max - x_min) / image_width
                        height = (y_max - y_min) / image_height
                        
                        # 写入YOLO格式
                        f.write(f"0 {center_x:.6f} {center_y:.6f} {width:.6f} {height:.6f}\n")
        
        return True
    except Exception as e:
        print(f"[错误] 转换失败: {e}")
        return False

def create_yolo_dataset(json_files, output_dir):
    """创建YOLO数据集"""
    print(f"[创建] 创建数据集到: {output_dir}")
    
    if not json_files:
        print("[错误] 没有找到标注文件")
        return False
    
    # 创建目录结构
    dirs_to_create = [
        os.path.join(output_dir, 'images', 'train'),
        os.path.join(output_dir, 'images', 'val'),
        os.path.join(output_dir, 'labels', 'train'),
        os.path.join(output_dir, 'labels', 'val')
    ]
    
    for dir_path in dirs_to_create:
        os.makedirs(dir_path, exist_ok=True)
    
    # 分割数据
    import random
    random.shuffle(json_files)
    split_idx = int(len(json_files) * 0.8)
    train_files = json_files[:split_idx]
    val_files = json_files[split_idx:]
    
    print(f"[分割] 训练集: {len(train_files)}, 验证集: {len(val_files)}")
    
    # 处理训练集
    train_count = 0
    for file_info in train_files:
        try:
            # 复制图像
            image_name = os.path.basename(file_info['image_path'])
            dest_image = os.path.join(output_dir, 'images', 'train', image_name)
            shutil.copy2(file_info['image_path'], dest_image)
            
            # 转换标注
            base_name = os.path.splitext(image_name)[0]
            label_name = f"{base_name}.txt"
            dest_label = os.path.join(output_dir, 'labels', 'train', label_name)
            
            if convert_json_to_yolo(file_info['json_path'], dest_label):
                train_count += 1
                print(f"  [训练] {image_name}")
        except Exception as e:
            print(f"  [错误] 处理训练文件失败: {e}")
    
    # 处理验证集
    val_count = 0
    for file_info in val_files:
        try:
            # 复制图像
            image_name = os.path.basename(file_info['image_path'])
            dest_image = os.path.join(output_dir, 'images', 'val', image_name)
            shutil.copy2(file_info['image_path'], dest_image)
            
            # 转换标注
            base_name = os.path.splitext(image_name)[0]
            label_name = f"{base_name}.txt"
            dest_label = os.path.join(output_dir, 'labels', 'val', label_name)
            
            if convert_json_to_yolo(file_info['json_path'], dest_label):
                val_count += 1
                print(f"  [验证] {image_name}")
        except Exception as e:
            print(f"  [错误] 处理验证文件失败: {e}")
    
    # 创建配置文件
    yaml_content = f"""# YOLO数据集配置文件
path: {os.path.abspath(output_dir)}
train: images/train
val: images/val

nc: 1
names: ['seed']

train_images: {train_count}
val_images: {val_count}
total_images: {train_count + val_count}
"""
    
    yaml_path = os.path.join(output_dir, 'dataset.yaml')
    with open(yaml_path, 'w', encoding='utf-8') as f:
        f.write(yaml_content)
    
    # 创建类别文件
    classes_path = os.path.join(output_dir, 'classes.txt')
    with open(classes_path, 'w', encoding='utf-8') as f:
        f.write("seed\n")
    
    print(f"[完成] 数据集创建完成!")
    print(f"  配置文件: {yaml_path}")
    print(f"  训练图像: {train_count}")
    print(f"  验证图像: {val_count}")
    
    return yaml_path

def main():
    """主函数"""
    setup_console_encoding()
    
    print("YOLO数据集修复工具 (简化版)")
    print("=" * 40)
    
    # 获取输入目录
    if len(sys.argv) > 1:
        input_dir = sys.argv[1]
    else:
        # 查找常见目录
        possible_dirs = ["output", "results", "segmentation_results", "."]
        input_dir = None
        
        for dir_name in possible_dirs:
            if os.path.exists(dir_name):
                input_dir = dir_name
                print(f"[自动] 使用目录: {dir_name}")
                break
        
        if not input_dir:
            print("[错误] 未找到输出目录")
            print("用法: python simple_yolo_fix.py [目录路径]")
            return 1
    
    print(f"[输入] 检查目录: {os.path.abspath(input_dir)}")
    
    # 查找标注文件
    json_files = find_json_files(input_dir)
    
    if not json_files:
        print("[失败] 没有找到YOLO标注文件")
        print("请确保:")
        print("1. 已经进行了图像分割")
        print("2. 启用了'生成YOLO标注文件'选项")
        print("3. 输出目录包含 *_yolo_annotations.json 文件")
        return 1
    
    # 创建数据集
    output_dir = os.path.join(input_dir, "yolo_dataset")
    yaml_path = create_yolo_dataset(json_files, output_dir)
    
    if yaml_path:
        print(f"\n[成功] 数据集创建成功!")
        print(f"[使用] 在GUI训练页面中选择文件:")
        print(f"  {yaml_path}")
        print(f"[提示] 或点击'使用最新分割数据集'按钮")
        return 0
    else:
        print(f"\n[失败] 数据集创建失败")
        return 1

if __name__ == "__main__":
    try:
        sys.exit(main())
    except Exception as e:
        print(f"[异常] 程序执行出错: {e}")
        sys.exit(1)
