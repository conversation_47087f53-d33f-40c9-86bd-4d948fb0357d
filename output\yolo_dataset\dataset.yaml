# YOLO数据集配置文件
# 由透明背景种子分割系统自动生成

# 数据集路径
path: C:\Users\<USER>\Desktop\DLproject\zhongzi\SAME\zhongzi\output\yolo_dataset

# 训练和验证数据路径
train: images/train
val: images/val

# 类别数量
nc: 105

# 类别名称
names: ['********-1', '********-1', '********-1', '********-1', '********-1', '********-1', '********-1', '********-1', '********-1', '********-1', '********-1', '********-1', '********-1', '********-1', '********-1', '********-1', '********-1', '********-1', '********-1', '********-1', '********-1', '********-1', '********-1', '********-1', '********-1', '********-1', '********-1', '********-1', '********-1', '********-1', '********-1', '********-1', '********-1', '********-1', '********-1', '********-1', '********-1', '********-1', '********-1', '********-1', '********-1', '********-1', '********-1', '********-1', '********-1', '********-1', '********-1', '********-1', '********-1', '********-1', '********-1', '********-1', '********-1', '********-1', '********-1', '********-1', '********-1', '********-1', '********-1', 'S0000154-1', 'S0000156-1', 'S0000159-1', 'S0000160-1', 'S0000164-1', 'S0000169-1', 'S0000172-1', 'S0000176-1', 'S0000183-1', 'S0000186-1', 'S0000189-1', 'S0000190-1', 'S0000193-1', 'S0000194-1', 'S0000198-1', 'S0000199-1', 'S0000202-1', 'S0000209-1', 'S0000214-1', 'S0000221-1', 'S0000223-1', 'S0000224-1', 'S0000225-1', 'S0000227-1', 'S0000229-1', 'S0000230-1', 'S0000231-1', 'S0000232-1', 'S0000234-1', 'S0000235-1', 'S0000236-1', 'S0000240-1', 'S0000241-1', 'S0000242-1', 'S0000248-1', 'S0000250-1', 'S0000251-1', 'S0000252-1', 'S0000253-1', 'S0000254-1', 'S0000256-1', 'S0000261-1', 'S0000268-1', 'S0000270-1', 'S0000272-1', 'S0000273-1']

# 数据集统计
total_samples: 105
train_samples: 84
val_samples: 21

# 生成信息
generated_by: "Transparent Seed Segmentation System"
description: "种子检测数据集，用于YOLO训练"
