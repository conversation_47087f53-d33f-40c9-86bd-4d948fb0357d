#!/usr/bin/env python3
"""
简单GUI测试
"""

print("开始测试...")

try:
    print("1. 导入tkinter...")
    import tkinter as tk
    print("✅ tkinter导入成功")
    
    print("2. 导入分类管理器...")
    from classification_manager import ClassificationManager
    print("✅ 分类管理器导入成功")
    
    print("3. 导入分类训练器...")
    from classification_trainer import ClassificationTrainer
    print("✅ 分类训练器导入成功")
    
    print("4. 导入分类识别器...")
    from classification_recognizer import ClassificationRecognizer
    print("✅ 分类识别器导入成功")
    
    print("5. 导入GUI组件...")
    from classification_gui_components import ClassificationTrainingPanel
    print("✅ GUI组件导入成功")
    
    print("6. 检查主GUI文件...")
    with open('enhanced_segmentation_gui.py', 'r') as f:
        content = f.read()
        if 'classification_manager' in content:
            print("✅ 主GUI包含分类管理器")
        else:
            print("❌ 主GUI缺少分类管理器")
    
    print("🎉 所有导入测试通过！")
    
except Exception as e:
    print(f"❌ 错误: {e}")
    import traceback
    traceback.print_exc()

print("测试完成")
