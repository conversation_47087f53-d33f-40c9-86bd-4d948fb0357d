#!/usr/bin/env python3
"""
测试YOLO模型加载问题的诊断脚本
"""

import os
import sys
import torch
import traceback
from pathlib import Path

def test_torch_loading():
    """测试PyTorch直接加载模型"""
    print("=== 测试PyTorch直接加载 ===")
    
    model_paths = [
        "yolo_models/yolov8n.pt",
        "yolo_models/yolov8l.pt",
        "yolov8n.pt"  # 根目录下的文件
    ]
    
    for model_path in model_paths:
        if os.path.exists(model_path):
            print(f"\n测试加载: {model_path}")
            try:
                # 尝试用不同的方式加载
                print("  方法1: torch.load with map_location='cpu'")
                model = torch.load(model_path, map_location='cpu')
                print(f"  ✓ 成功加载，类型: {type(model)}")
                if isinstance(model, dict):
                    print(f"  模型键: {list(model.keys())}")
                
                print("  方法2: torch.load with map_location='cuda'")
                model_cuda = torch.load(model_path, map_location='cuda:0')
                print(f"  ✓ 成功加载到CUDA，类型: {type(model_cuda)}")
                
            except Exception as e:
                print(f"  ✗ 加载失败: {e}")
                print(f"  错误类型: {type(e).__name__}")
                traceback.print_exc()
        else:
            print(f"文件不存在: {model_path}")

def test_ultralytics_loading():
    """测试Ultralytics YOLO加载"""
    print("\n=== 测试Ultralytics YOLO加载 ===")
    
    try:
        from ultralytics import YOLO
        print("✓ Ultralytics导入成功")
        
        model_paths = [
            "yolo_models/yolov8n.pt",
            "yolo_models/yolov8l.pt"
        ]
        
        for model_path in model_paths:
            if os.path.exists(model_path):
                print(f"\n测试YOLO加载: {model_path}")
                try:
                    model = YOLO(model_path)
                    print(f"  ✓ 成功加载YOLO模型")
                    print(f"  模型任务: {getattr(model, 'task', 'unknown')}")
                    print(f"  模型名称: {getattr(model, 'names', {})}")
                    
                except Exception as e:
                    print(f"  ✗ YOLO加载失败: {e}")
                    print(f"  错误类型: {type(e).__name__}")
                    traceback.print_exc()
            else:
                print(f"文件不存在: {model_path}")
                
    except ImportError as e:
        print(f"✗ Ultralytics导入失败: {e}")

def test_file_integrity():
    """测试文件完整性"""
    print("\n=== 测试文件完整性 ===")
    
    model_paths = [
        "yolo_models/yolov8n.pt",
        "yolo_models/yolov8l.pt"
    ]
    
    for model_path in model_paths:
        if os.path.exists(model_path):
            print(f"\n检查文件: {model_path}")
            
            # 检查文件大小
            file_size = os.path.getsize(model_path)
            print(f"  文件大小: {file_size:,} bytes ({file_size/1024/1024:.1f} MB)")
            
            # 检查文件是否可读
            try:
                with open(model_path, 'rb') as f:
                    header = f.read(100)
                    print(f"  文件头: {header[:50]}...")
                    print(f"  ✓ 文件可读")
            except Exception as e:
                print(f"  ✗ 文件读取失败: {e}")
                
            # 尝试作为zip文件检查
            try:
                import zipfile
                with zipfile.ZipFile(model_path, 'r') as zf:
                    files = zf.namelist()
                    print(f"  ✓ ZIP文件有效，包含 {len(files)} 个文件")
                    print(f"  主要文件: {files[:5]}...")
                    
                    # 测试解压
                    zf.testzip()
                    print(f"  ✓ ZIP文件完整性检查通过")
                    
            except Exception as e:
                print(f"  ✗ ZIP检查失败: {e}")

def test_environment():
    """测试环境信息"""
    print("=== 环境信息 ===")
    print(f"Python版本: {sys.version}")
    print(f"PyTorch版本: {torch.__version__}")
    print(f"CUDA可用: {torch.cuda.is_available()}")
    if torch.cuda.is_available():
        print(f"CUDA版本: {torch.version.cuda}")
        print(f"GPU数量: {torch.cuda.device_count()}")
        print(f"当前GPU: {torch.cuda.current_device()}")
        print(f"GPU名称: {torch.cuda.get_device_name()}")
    
    try:
        import ultralytics
        print(f"Ultralytics版本: {ultralytics.__version__}")
    except ImportError:
        print("Ultralytics未安装")
    
    print(f"工作目录: {os.getcwd()}")

def main():
    """主函数"""
    print("YOLO模型加载问题诊断")
    print("=" * 50)
    
    test_environment()
    test_file_integrity()
    test_torch_loading()
    test_ultralytics_loading()
    
    print("\n=== 诊断完成 ===")

if __name__ == "__main__":
    main()
