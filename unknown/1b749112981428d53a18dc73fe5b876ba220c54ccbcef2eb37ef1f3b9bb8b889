#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Comprehensive tests for mask-based segmentation functionality
- Test mask processing and validation
- Test connected component analysis
- Test seed cropping and saving
- Test edge cases and error handling
"""

import os
import sys
import cv2
import numpy as np
import tempfile
import shutil
import unittest
from pathlib import Path

# Add current directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from mask_based_segmentation import MaskBasedSegmentation
    MASK_SEGMENTATION_AVAILABLE = True
except ImportError:
    print("Warning: MaskBasedSegmentation not available")
    MASK_SEGMENTATION_AVAILABLE = False

class TestMaskBasedSegmentation(unittest.TestCase):
    """Test cases for mask-based segmentation"""
    
    def setUp(self):
        """Set up test environment"""
        if not MASK_SEGMENTATION_AVAILABLE:
            self.skipTest("MaskBasedSegmentation not available")
        
        # Create temporary directory for test outputs
        self.test_dir = tempfile.mkdtemp()
        
        # Default configuration
        self.config = {
            'min_seed_area': 100,
            'max_seed_area': 50000,
            'padding': 10,
            'binary_threshold': 127,
            'invert_mask': True,
            'connectivity': 8,
            'output_format': 'png',
        }
        
        # Initialize segmentation system
        self.segmentation = MaskBasedSegmentation(self.config)
    
    def tearDown(self):
        """Clean up test environment"""
        if hasattr(self, 'test_dir') and os.path.exists(self.test_dir):
            shutil.rmtree(self.test_dir)
    
    def create_test_mask(self, width=400, height=300, num_seeds=3):
        """Create a test mask image with black seeds on white background"""
        # Create white background
        mask = np.ones((height, width), dtype=np.uint8) * 255
        
        # Add black seed regions
        seed_info = []
        
        # Seed 1: Large oval
        center1 = (100, 100)
        axes1 = (40, 60)
        cv2.ellipse(mask, center1, axes1, 0, 0, 360, 0, -1)
        seed_info.append({'center': center1, 'size': axes1, 'type': 'ellipse'})
        
        # Seed 2: Medium circle
        if num_seeds > 1:
            center2 = (250, 150)
            radius2 = 30
            cv2.circle(mask, center2, radius2, 0, -1)
            seed_info.append({'center': center2, 'size': radius2, 'type': 'circle'})
        
        # Seed 3: Small rectangle (could be scale bar)
        if num_seeds > 2:
            cv2.rectangle(mask, (320, 250), (380, 260), 0, -1)
            seed_info.append({'center': (350, 255), 'size': (60, 10), 'type': 'rectangle'})
        
        return mask, seed_info
    
    def create_test_original(self, width=400, height=300):
        """Create a test original image"""
        # Create a colorful test image
        original = np.zeros((height, width, 3), dtype=np.uint8)
        
        # Add some patterns
        original[:, :, 0] = np.linspace(0, 255, width, dtype=np.uint8)  # Red gradient
        original[:, :, 1] = np.linspace(0, 255, height, dtype=np.uint8).reshape(-1, 1)  # Green gradient
        original[:, :, 2] = 128  # Constant blue
        
        # Add some noise for realism
        noise = np.random.randint(0, 50, (height, width, 3), dtype=np.uint8)
        original = cv2.add(original, noise)
        
        return original
    
    def test_initialization(self):
        """Test segmentation system initialization"""
        self.assertIsNotNone(self.segmentation)
        self.assertEqual(self.segmentation.config['min_seed_area'], 100)
        self.assertEqual(self.segmentation.config['max_seed_area'], 50000)
        self.assertTrue(self.segmentation.config['invert_mask'])
    
    def test_mask_validation(self):
        """Test mask image validation"""
        # Valid mask
        valid_mask, _ = self.create_test_mask()
        self.assertTrue(self.segmentation.validate_mask_image(valid_mask))
        
        # Invalid masks
        self.assertFalse(self.segmentation.validate_mask_image(None))
        self.assertFalse(self.segmentation.validate_mask_image(np.array([1, 2, 3])))  # Wrong shape
        self.assertFalse(self.segmentation.validate_mask_image(np.ones((5, 5))))  # Too small
    
    def test_mask_preprocessing(self):
        """Test mask preprocessing"""
        mask, _ = self.create_test_mask()
        
        # Test preprocessing
        binary_mask = self.segmentation.preprocess_mask(mask)
        
        # Check output properties
        self.assertEqual(len(binary_mask.shape), 2)  # Should be grayscale
        self.assertEqual(binary_mask.dtype, np.uint8)
        self.assertTrue(np.all((binary_mask == 0) | (binary_mask == 255)))  # Should be binary
        
        # Check inversion (black seeds should become white in binary mask)
        # Original mask has black seeds (0) on white background (255)
        # After inversion, seeds should be white (255) in binary mask
        seed_pixels = np.sum(binary_mask == 255)
        background_pixels = np.sum(binary_mask == 0)
        self.assertGreater(background_pixels, seed_pixels)  # More background than seeds
    
    def test_connected_components(self):
        """Test connected component analysis"""
        mask, seed_info = self.create_test_mask(num_seeds=3)
        binary_mask = self.segmentation.preprocess_mask(mask)
        
        components, labeled_image = self.segmentation.find_connected_components(binary_mask)
        
        # Should find components (exact number may vary due to morphological operations)
        self.assertGreater(len(components), 0)
        self.assertLessEqual(len(components), 3)  # Should not exceed expected seeds
        
        # Check component properties
        for comp in components:
            self.assertIn('label', comp)
            self.assertIn('area', comp)
            self.assertIn('bbox', comp)
            self.assertIn('centroid', comp)
            self.assertIn('aspect_ratio', comp)
            
            # Area should be within configured limits
            self.assertGreaterEqual(comp['area'], self.config['min_seed_area'])
            self.assertLessEqual(comp['area'], self.config['max_seed_area'])
    
    def test_scale_bar_detection(self):
        """Test scale bar detection"""
        # Create mask with a scale bar (high aspect ratio)
        mask = np.ones((300, 400), dtype=np.uint8) * 255
        
        # Add a seed (low aspect ratio)
        cv2.circle(mask, (100, 150), 30, 0, -1)
        
        # Add a scale bar (high aspect ratio)
        cv2.rectangle(mask, (250, 200), (350, 210), 0, -1)  # 100x10 rectangle
        
        binary_mask = self.segmentation.preprocess_mask(mask)
        components, _ = self.segmentation.find_connected_components(binary_mask)
        
        seeds, scale_bars = self.segmentation.detect_scale_bars(components)
        
        # Should separate seeds from scale bars
        self.assertGreater(len(seeds), 0)
        # Note: Scale bar detection depends on area ratios, so may not always detect
        # This is expected behavior
    
    def test_seed_cropping(self):
        """Test seed cropping from original image"""
        mask, _ = self.create_test_mask(num_seeds=2)
        original = self.create_test_original()
        
        binary_mask = self.segmentation.preprocess_mask(mask)
        components, _ = self.segmentation.find_connected_components(binary_mask)
        
        if components:
            component = components[0]
            cropped = self.segmentation.crop_seed_from_original(original, component)
            
            self.assertIsNotNone(cropped)
            self.assertEqual(len(cropped.shape), 3)  # Should be color image
            self.assertGreater(cropped.shape[0], 0)  # Height > 0
            self.assertGreater(cropped.shape[1], 0)  # Width > 0
    
    def test_seed_saving(self):
        """Test saving cropped seeds"""
        # Create a test cropped image
        test_crop = np.random.randint(0, 255, (50, 60, 3), dtype=np.uint8)
        
        # Save seed
        result = self.segmentation.save_cropped_seed(
            test_crop, 1, self.test_dir, "test_image.jpg"
        )
        
        self.assertIsNotNone(result)
        self.assertEqual(result['seed_id'], 1)
        self.assertTrue(os.path.exists(result['path']))
        self.assertEqual(result['format'], 'png')  # Default format
        
        # Check file was actually saved
        saved_image = cv2.imread(result['path'])
        self.assertIsNotNone(saved_image)
    
    def test_full_processing_pipeline(self):
        """Test complete processing pipeline"""
        # Create test images
        mask, _ = self.create_test_mask(num_seeds=2)
        original = self.create_test_original()
        
        # Save test images
        mask_path = os.path.join(self.test_dir, "test_mask.png")
        original_path = os.path.join(self.test_dir, "test_original.jpg")
        
        cv2.imwrite(mask_path, mask)
        cv2.imwrite(original_path, original)
        
        # Process
        result = self.segmentation.process_mask_and_original(
            mask_path, original_path, self.test_dir
        )
        
        # Check results
        self.assertTrue(result['success'])
        self.assertGreater(result['seeds_count'], 0)
        self.assertGreaterEqual(result['scale_bars_count'], 0)
        self.assertGreater(result['processing_time'], 0)
        self.assertEqual(result['method'], 'mask_based_segmentation')
        
        # Check output files exist
        self.assertTrue(os.path.exists(result['visualization_path']))
        
        # Check seed files
        for seed in result['seeds']:
            self.assertTrue(os.path.exists(seed['path']))
    
    def test_error_handling(self):
        """Test error handling for various edge cases"""
        # Test with non-existent files
        result = self.segmentation.process_mask_and_original(
            "nonexistent_mask.png", "nonexistent_original.jpg", self.test_dir
        )
        self.assertFalse(result['success'])
        self.assertIn('error', result)
        
        # Test with empty mask
        empty_mask = np.ones((100, 100), dtype=np.uint8) * 255  # All white, no seeds
        empty_mask_path = os.path.join(self.test_dir, "empty_mask.png")
        original_path = os.path.join(self.test_dir, "test_original.jpg")
        
        cv2.imwrite(empty_mask_path, empty_mask)
        cv2.imwrite(original_path, self.create_test_original(100, 100))
        
        result = self.segmentation.process_mask_and_original(
            empty_mask_path, original_path, self.test_dir
        )
        self.assertFalse(result['success'])
        self.assertIn('No valid seed components found', result['error'])
    
    def test_configuration_options(self):
        """Test different configuration options"""
        # Test with different parameters
        custom_config = {
            'min_seed_area': 50,
            'max_seed_area': 10000,
            'padding': 20,
            'invert_mask': False,  # White seeds on black background
            'output_format': 'jpg',
        }
        
        custom_segmentation = MaskBasedSegmentation(custom_config)
        
        # Check configuration was applied
        self.assertEqual(custom_segmentation.config['min_seed_area'], 50)
        self.assertEqual(custom_segmentation.config['padding'], 20)
        self.assertFalse(custom_segmentation.config['invert_mask'])
        self.assertEqual(custom_segmentation.config['output_format'], 'jpg')

def create_sample_test_images():
    """Create sample test images for manual testing"""
    print("Creating sample test images...")
    
    # Create test mask (black seeds on white background)
    mask = np.ones((400, 600), dtype=np.uint8) * 255
    
    # Add various seed shapes
    cv2.ellipse(mask, (150, 150), (50, 70), 0, 0, 360, 0, -1)  # Large seed
    cv2.circle(mask, (350, 150), 40, 0, -1)  # Medium seed
    cv2.circle(mask, (500, 150), 25, 0, -1)  # Small seed
    cv2.rectangle(mask, (450, 300), (550, 310), 0, -1)  # Scale bar
    
    # Create colorful original image
    original = np.zeros((400, 600, 3), dtype=np.uint8)
    original[:, :, 0] = np.linspace(50, 200, 600, dtype=np.uint8)
    original[:, :, 1] = np.linspace(30, 180, 400, dtype=np.uint8).reshape(-1, 1)
    original[:, :, 2] = 100
    
    # Add some texture
    noise = np.random.randint(0, 30, (400, 600, 3), dtype=np.uint8)
    original = cv2.add(original, noise)
    
    # Save images
    cv2.imwrite("sample_mask.png", mask)
    cv2.imwrite("sample_original.jpg", original)
    
    print("Sample images created:")
    print("  - sample_mask.png (mask with black seeds on white background)")
    print("  - sample_original.jpg (colorful original image)")
    print("\nTo test manually:")
    print("  python segmentation_cli.py --method mask --mask-image sample_mask.png --input sample_original.jpg --output test_output")

def main():
    """Run tests"""
    if len(sys.argv) > 1 and sys.argv[1] == "create-samples":
        create_sample_test_images()
        return
    
    # Run unit tests
    unittest.main(verbosity=2)

if __name__ == "__main__":
    main()
