2025-06-22 21:32:43,900 - __main__ - INFO - 透明背景种子分割系统配置:
2025-06-22 21:32:43,900 - __main__ - INFO -   min_seed_area: 100
2025-06-22 21:32:43,900 - __main__ - INFO -   max_seed_area: 100000
2025-06-22 21:32:43,900 - __main__ - INFO -   padding: 10
2025-06-22 21:32:43,900 - __main__ - INFO -   alpha_threshold: 128
2025-06-22 21:32:43,900 - __main__ - INFO -   connectivity: 8
2025-06-22 21:32:43,900 - __main__ - INFO -   morphology_kernel_size: 3
2025-06-22 21:32:43,900 - __main__ - INFO -   min_alpha_ratio: 0.1
2025-06-22 21:32:43,900 - __main__ - INFO -   output_format: png
2025-06-22 21:32:43,901 - __main__ - INFO -   preserve_quality: True
2025-06-22 21:32:43,901 - __main__ - INFO -   remove_noise: True
2025-06-22 21:32:43,901 - __main__ - INFO - 初始化透明背景种子分割系统...
2025-06-22 21:32:43,901 - transparent_seed_segmentation - INFO - 透明背景种子分割系统初始化完成
2025-06-22 21:32:43,901 - __main__ - INFO - 批量处理透明背景图像:
2025-06-22 21:32:43,901 - __main__ - INFO -   输入目录: transparent_demo
2025-06-22 21:32:43,901 - __main__ - INFO -   输出目录: transparent_demo_results
2025-06-22 21:32:43,901 - __main__ - INFO -   支持格式: ['.png']
2025-06-22 21:32:43,902 - transparent_seed_segmentation - INFO - 找到 5 个图像文件进行批量处理
2025-06-22 21:32:43,902 - transparent_seed_segmentation - INFO - 处理文件 1/5: 单个大种子.png
2025-06-22 21:32:43,902 - transparent_seed_segmentation - INFO - 读取透明背景图像: transparent_demo\单个大种子.png
2025-06-22 21:32:43,912 - PIL.PngImagePlugin - DEBUG - STREAM b'IHDR' 16 13
2025-06-22 21:32:43,912 - PIL.PngImagePlugin - DEBUG - STREAM b'IDAT' 41 2299
2025-06-22 21:32:43,919 - transparent_seed_segmentation - INFO - 分析alpha通道...
2025-06-22 21:32:43,920 - transparent_seed_segmentation - INFO - 查找种子组件...
2025-06-22 21:32:43,921 - transparent_seed_segmentation - ERROR - 处理失败: OpenCV(4.6.0) :-1: error: (-5:Bad argument) in function 'connectedComponentsWithStats'
> Overload resolution failed:
>  - 'dtype' is an invalid keyword argument for connectedComponentsWithStats()
>  - 'dtype' is an invalid keyword argument for connectedComponentsWithStats()

2025-06-22 21:32:43,921 - transparent_seed_segmentation - ERROR -   ✗ 处理失败: OpenCV(4.6.0) :-1: error: (-5:Bad argument) in function 'connectedComponentsWithStats'
> Overload resolution failed:
>  - 'dtype' is an invalid keyword argument for connectedComponentsWithStats()
>  - 'dtype' is an invalid keyword argument for connectedComponentsWithStats()

2025-06-22 21:32:43,922 - transparent_seed_segmentation - INFO - 处理文件 2/5: 复杂形状种子.png
2025-06-22 21:32:43,922 - transparent_seed_segmentation - INFO - 读取透明背景图像: transparent_demo\复杂形状种子.png
2025-06-22 21:32:43,922 - PIL.PngImagePlugin - DEBUG - STREAM b'IHDR' 16 13
2025-06-22 21:32:43,922 - PIL.PngImagePlugin - DEBUG - STREAM b'IDAT' 41 2283
2025-06-22 21:32:43,924 - transparent_seed_segmentation - INFO - 分析alpha通道...
2025-06-22 21:32:43,925 - transparent_seed_segmentation - INFO - 查找种子组件...
2025-06-22 21:32:43,925 - transparent_seed_segmentation - ERROR - 处理失败: OpenCV(4.6.0) :-1: error: (-5:Bad argument) in function 'connectedComponentsWithStats'
> Overload resolution failed:
>  - 'dtype' is an invalid keyword argument for connectedComponentsWithStats()
>  - 'dtype' is an invalid keyword argument for connectedComponentsWithStats()

2025-06-22 21:32:43,925 - transparent_seed_segmentation - ERROR -   ✗ 处理失败: OpenCV(4.6.0) :-1: error: (-5:Bad argument) in function 'connectedComponentsWithStats'
> Overload resolution failed:
>  - 'dtype' is an invalid keyword argument for connectedComponentsWithStats()
>  - 'dtype' is an invalid keyword argument for connectedComponentsWithStats()

2025-06-22 21:32:43,925 - transparent_seed_segmentation - INFO - 处理文件 3/5: 多种子样本.png
2025-06-22 21:32:43,925 - transparent_seed_segmentation - INFO - 读取透明背景图像: transparent_demo\多种子样本.png
2025-06-22 21:32:43,925 - PIL.PngImagePlugin - DEBUG - STREAM b'IHDR' 16 13
2025-06-22 21:32:43,925 - PIL.PngImagePlugin - DEBUG - STREAM b'IDAT' 41 2956
2025-06-22 21:32:43,927 - transparent_seed_segmentation - INFO - 分析alpha通道...
2025-06-22 21:32:43,927 - transparent_seed_segmentation - INFO - 查找种子组件...
2025-06-22 21:32:43,929 - transparent_seed_segmentation - ERROR - 处理失败: OpenCV(4.6.0) :-1: error: (-5:Bad argument) in function 'connectedComponentsWithStats'
> Overload resolution failed:
>  - 'dtype' is an invalid keyword argument for connectedComponentsWithStats()
>  - 'dtype' is an invalid keyword argument for connectedComponentsWithStats()

2025-06-22 21:32:43,929 - transparent_seed_segmentation - ERROR -   ✗ 处理失败: OpenCV(4.6.0) :-1: error: (-5:Bad argument) in function 'connectedComponentsWithStats'
> Overload resolution failed:
>  - 'dtype' is an invalid keyword argument for connectedComponentsWithStats()
>  - 'dtype' is an invalid keyword argument for connectedComponentsWithStats()

2025-06-22 21:32:43,929 - transparent_seed_segmentation - INFO - 处理文件 4/5: 大小种子混合.png
2025-06-22 21:32:43,929 - transparent_seed_segmentation - INFO - 读取透明背景图像: transparent_demo\大小种子混合.png
2025-06-22 21:32:43,931 - PIL.PngImagePlugin - DEBUG - STREAM b'IHDR' 16 13
2025-06-22 21:32:43,931 - PIL.PngImagePlugin - DEBUG - STREAM b'IDAT' 41 2374
2025-06-22 21:32:43,933 - transparent_seed_segmentation - INFO - 分析alpha通道...
2025-06-22 21:32:43,933 - transparent_seed_segmentation - INFO - 查找种子组件...
2025-06-22 21:32:43,934 - transparent_seed_segmentation - ERROR - 处理失败: OpenCV(4.6.0) :-1: error: (-5:Bad argument) in function 'connectedComponentsWithStats'
> Overload resolution failed:
>  - 'dtype' is an invalid keyword argument for connectedComponentsWithStats()
>  - 'dtype' is an invalid keyword argument for connectedComponentsWithStats()

2025-06-22 21:32:43,935 - transparent_seed_segmentation - ERROR -   ✗ 处理失败: OpenCV(4.6.0) :-1: error: (-5:Bad argument) in function 'connectedComponentsWithStats'
> Overload resolution failed:
>  - 'dtype' is an invalid keyword argument for connectedComponentsWithStats()
>  - 'dtype' is an invalid keyword argument for connectedComponentsWithStats()

2025-06-22 21:32:43,935 - transparent_seed_segmentation - INFO - 处理文件 5/5: 边缘种子.png
2025-06-22 21:32:43,935 - transparent_seed_segmentation - INFO - 读取透明背景图像: transparent_demo\边缘种子.png
2025-06-22 21:32:43,935 - PIL.PngImagePlugin - DEBUG - STREAM b'IHDR' 16 13
2025-06-22 21:32:43,936 - PIL.PngImagePlugin - DEBUG - STREAM b'IDAT' 41 2465
2025-06-22 21:32:43,938 - transparent_seed_segmentation - INFO - 分析alpha通道...
2025-06-22 21:32:43,939 - transparent_seed_segmentation - INFO - 查找种子组件...
2025-06-22 21:32:43,939 - transparent_seed_segmentation - ERROR - 处理失败: OpenCV(4.6.0) :-1: error: (-5:Bad argument) in function 'connectedComponentsWithStats'
> Overload resolution failed:
>  - 'dtype' is an invalid keyword argument for connectedComponentsWithStats()
>  - 'dtype' is an invalid keyword argument for connectedComponentsWithStats()

2025-06-22 21:32:43,939 - transparent_seed_segmentation - ERROR -   ✗ 处理失败: OpenCV(4.6.0) :-1: error: (-5:Bad argument) in function 'connectedComponentsWithStats'
> Overload resolution failed:
>  - 'dtype' is an invalid keyword argument for connectedComponentsWithStats()
>  - 'dtype' is an invalid keyword argument for connectedComponentsWithStats()

2025-06-22 21:32:43,939 - __main__ - INFO - 批量处理完成!
2025-06-22 21:32:43,940 - __main__ - INFO -   处理文件: 0/5
2025-06-22 21:32:43,940 - __main__ - INFO -   总种子数: 0
2025-06-22 21:32:43,940 - __main__ - INFO -   处理时间: 0.04秒
2025-06-22 21:32:43,940 - __main__ - WARNING - 失败文件 (5):
2025-06-22 21:32:43,940 - __main__ - WARNING -   单个大种子.png: OpenCV(4.6.0) :-1: error: (-5:Bad argument) in function 'connectedComponentsWithStats'
> Overload resolution failed:
>  - 'dtype' is an invalid keyword argument for connectedComponentsWithStats()
>  - 'dtype' is an invalid keyword argument for connectedComponentsWithStats()

2025-06-22 21:32:43,940 - __main__ - WARNING -   复杂形状种子.png: OpenCV(4.6.0) :-1: error: (-5:Bad argument) in function 'connectedComponentsWithStats'
> Overload resolution failed:
>  - 'dtype' is an invalid keyword argument for connectedComponentsWithStats()
>  - 'dtype' is an invalid keyword argument for connectedComponentsWithStats()

2025-06-22 21:32:43,940 - __main__ - WARNING -   多种子样本.png: OpenCV(4.6.0) :-1: error: (-5:Bad argument) in function 'connectedComponentsWithStats'
> Overload resolution failed:
>  - 'dtype' is an invalid keyword argument for connectedComponentsWithStats()
>  - 'dtype' is an invalid keyword argument for connectedComponentsWithStats()

2025-06-22 21:32:43,940 - __main__ - WARNING -   大小种子混合.png: OpenCV(4.6.0) :-1: error: (-5:Bad argument) in function 'connectedComponentsWithStats'
> Overload resolution failed:
>  - 'dtype' is an invalid keyword argument for connectedComponentsWithStats()
>  - 'dtype' is an invalid keyword argument for connectedComponentsWithStats()

2025-06-22 21:32:43,940 - __main__ - WARNING -   边缘种子.png: OpenCV(4.6.0) :-1: error: (-5:Bad argument) in function 'connectedComponentsWithStats'
> Overload resolution failed:
>  - 'dtype' is an invalid keyword argument for connectedComponentsWithStats()
>  - 'dtype' is an invalid keyword argument for connectedComponentsWithStats()

2025-06-22 21:32:43,940 - __main__ - INFO - 总处理时间: 0.04秒
2025-06-22 21:33:11,749 - __main__ - INFO - 透明背景种子分割系统配置:
2025-06-22 21:33:11,750 - __main__ - INFO -   min_seed_area: 100
2025-06-22 21:33:11,750 - __main__ - INFO -   max_seed_area: 100000
2025-06-22 21:33:11,750 - __main__ - INFO -   padding: 10
2025-06-22 21:33:11,750 - __main__ - INFO -   alpha_threshold: 128
2025-06-22 21:33:11,750 - __main__ - INFO -   connectivity: 8
2025-06-22 21:33:11,750 - __main__ - INFO -   morphology_kernel_size: 3
2025-06-22 21:33:11,750 - __main__ - INFO -   min_alpha_ratio: 0.1
2025-06-22 21:33:11,750 - __main__ - INFO -   output_format: png
2025-06-22 21:33:11,750 - __main__ - INFO -   preserve_quality: True
2025-06-22 21:33:11,750 - __main__ - INFO -   remove_noise: True
2025-06-22 21:33:11,751 - __main__ - INFO - 初始化透明背景种子分割系统...
2025-06-22 21:33:11,751 - transparent_seed_segmentation - INFO - 透明背景种子分割系统初始化完成
2025-06-22 21:33:11,752 - __main__ - INFO - 批量处理透明背景图像:
2025-06-22 21:33:11,752 - __main__ - INFO -   输入目录: transparent_demo
2025-06-22 21:33:11,753 - __main__ - INFO -   输出目录: transparent_demo_results
2025-06-22 21:33:11,753 - __main__ - INFO -   支持格式: ['.png']
2025-06-22 21:33:11,754 - transparent_seed_segmentation - INFO - 找到 5 个图像文件进行批量处理
2025-06-22 21:33:11,755 - transparent_seed_segmentation - INFO - 处理文件 1/5: 单个大种子.png
2025-06-22 21:33:11,755 - transparent_seed_segmentation - INFO - 读取透明背景图像: transparent_demo\单个大种子.png
2025-06-22 21:33:11,765 - PIL.PngImagePlugin - DEBUG - STREAM b'IHDR' 16 13
2025-06-22 21:33:11,766 - PIL.PngImagePlugin - DEBUG - STREAM b'IDAT' 41 2299
2025-06-22 21:33:11,770 - transparent_seed_segmentation - INFO - 分析alpha通道...
2025-06-22 21:33:11,771 - transparent_seed_segmentation - INFO - 查找种子组件...
2025-06-22 21:33:11,771 - transparent_seed_segmentation - ERROR - 处理失败: OpenCV(4.6.0) :-1: error: (-5:Bad argument) in function 'connectedComponentsWithStats'
> Overload resolution failed:
>  - 'dtype' is an invalid keyword argument for connectedComponentsWithStats()
>  - 'dtype' is an invalid keyword argument for connectedComponentsWithStats()

2025-06-22 21:33:11,771 - transparent_seed_segmentation - ERROR -   ✗ 处理失败: OpenCV(4.6.0) :-1: error: (-5:Bad argument) in function 'connectedComponentsWithStats'
> Overload resolution failed:
>  - 'dtype' is an invalid keyword argument for connectedComponentsWithStats()
>  - 'dtype' is an invalid keyword argument for connectedComponentsWithStats()

2025-06-22 21:33:11,771 - transparent_seed_segmentation - INFO - 处理文件 2/5: 复杂形状种子.png
2025-06-22 21:33:11,771 - transparent_seed_segmentation - INFO - 读取透明背景图像: transparent_demo\复杂形状种子.png
2025-06-22 21:33:11,771 - PIL.PngImagePlugin - DEBUG - STREAM b'IHDR' 16 13
2025-06-22 21:33:11,772 - PIL.PngImagePlugin - DEBUG - STREAM b'IDAT' 41 2283
2025-06-22 21:33:11,773 - transparent_seed_segmentation - INFO - 分析alpha通道...
2025-06-22 21:33:11,774 - transparent_seed_segmentation - INFO - 查找种子组件...
2025-06-22 21:33:11,774 - transparent_seed_segmentation - ERROR - 处理失败: OpenCV(4.6.0) :-1: error: (-5:Bad argument) in function 'connectedComponentsWithStats'
> Overload resolution failed:
>  - 'dtype' is an invalid keyword argument for connectedComponentsWithStats()
>  - 'dtype' is an invalid keyword argument for connectedComponentsWithStats()

2025-06-22 21:33:11,774 - transparent_seed_segmentation - ERROR -   ✗ 处理失败: OpenCV(4.6.0) :-1: error: (-5:Bad argument) in function 'connectedComponentsWithStats'
> Overload resolution failed:
>  - 'dtype' is an invalid keyword argument for connectedComponentsWithStats()
>  - 'dtype' is an invalid keyword argument for connectedComponentsWithStats()

2025-06-22 21:33:11,774 - transparent_seed_segmentation - INFO - 处理文件 3/5: 多种子样本.png
2025-06-22 21:33:11,774 - transparent_seed_segmentation - INFO - 读取透明背景图像: transparent_demo\多种子样本.png
2025-06-22 21:33:11,774 - PIL.PngImagePlugin - DEBUG - STREAM b'IHDR' 16 13
2025-06-22 21:33:11,774 - PIL.PngImagePlugin - DEBUG - STREAM b'IDAT' 41 2956
2025-06-22 21:33:11,776 - transparent_seed_segmentation - INFO - 分析alpha通道...
2025-06-22 21:33:11,776 - transparent_seed_segmentation - INFO - 查找种子组件...
2025-06-22 21:33:11,776 - transparent_seed_segmentation - ERROR - 处理失败: OpenCV(4.6.0) :-1: error: (-5:Bad argument) in function 'connectedComponentsWithStats'
> Overload resolution failed:
>  - 'dtype' is an invalid keyword argument for connectedComponentsWithStats()
>  - 'dtype' is an invalid keyword argument for connectedComponentsWithStats()

2025-06-22 21:33:11,776 - transparent_seed_segmentation - ERROR -   ✗ 处理失败: OpenCV(4.6.0) :-1: error: (-5:Bad argument) in function 'connectedComponentsWithStats'
> Overload resolution failed:
>  - 'dtype' is an invalid keyword argument for connectedComponentsWithStats()
>  - 'dtype' is an invalid keyword argument for connectedComponentsWithStats()

2025-06-22 21:33:11,776 - transparent_seed_segmentation - INFO - 处理文件 4/5: 大小种子混合.png
2025-06-22 21:33:11,776 - transparent_seed_segmentation - INFO - 读取透明背景图像: transparent_demo\大小种子混合.png
2025-06-22 21:33:11,777 - PIL.PngImagePlugin - DEBUG - STREAM b'IHDR' 16 13
2025-06-22 21:33:11,777 - PIL.PngImagePlugin - DEBUG - STREAM b'IDAT' 41 2374
2025-06-22 21:33:11,778 - transparent_seed_segmentation - INFO - 分析alpha通道...
2025-06-22 21:33:11,779 - transparent_seed_segmentation - INFO - 查找种子组件...
2025-06-22 21:33:11,779 - transparent_seed_segmentation - ERROR - 处理失败: OpenCV(4.6.0) :-1: error: (-5:Bad argument) in function 'connectedComponentsWithStats'
> Overload resolution failed:
>  - 'dtype' is an invalid keyword argument for connectedComponentsWithStats()
>  - 'dtype' is an invalid keyword argument for connectedComponentsWithStats()

2025-06-22 21:33:11,779 - transparent_seed_segmentation - ERROR -   ✗ 处理失败: OpenCV(4.6.0) :-1: error: (-5:Bad argument) in function 'connectedComponentsWithStats'
> Overload resolution failed:
>  - 'dtype' is an invalid keyword argument for connectedComponentsWithStats()
>  - 'dtype' is an invalid keyword argument for connectedComponentsWithStats()

2025-06-22 21:33:11,779 - transparent_seed_segmentation - INFO - 处理文件 5/5: 边缘种子.png
2025-06-22 21:33:11,779 - transparent_seed_segmentation - INFO - 读取透明背景图像: transparent_demo\边缘种子.png
2025-06-22 21:33:11,779 - PIL.PngImagePlugin - DEBUG - STREAM b'IHDR' 16 13
2025-06-22 21:33:11,779 - PIL.PngImagePlugin - DEBUG - STREAM b'IDAT' 41 2465
2025-06-22 21:33:11,781 - transparent_seed_segmentation - INFO - 分析alpha通道...
2025-06-22 21:33:11,781 - transparent_seed_segmentation - INFO - 查找种子组件...
2025-06-22 21:33:11,781 - transparent_seed_segmentation - ERROR - 处理失败: OpenCV(4.6.0) :-1: error: (-5:Bad argument) in function 'connectedComponentsWithStats'
> Overload resolution failed:
>  - 'dtype' is an invalid keyword argument for connectedComponentsWithStats()
>  - 'dtype' is an invalid keyword argument for connectedComponentsWithStats()

2025-06-22 21:33:11,781 - transparent_seed_segmentation - ERROR -   ✗ 处理失败: OpenCV(4.6.0) :-1: error: (-5:Bad argument) in function 'connectedComponentsWithStats'
> Overload resolution failed:
>  - 'dtype' is an invalid keyword argument for connectedComponentsWithStats()
>  - 'dtype' is an invalid keyword argument for connectedComponentsWithStats()

2025-06-22 21:33:11,782 - __main__ - INFO - 批量处理完成!
2025-06-22 21:33:11,782 - __main__ - INFO -   处理文件: 0/5
2025-06-22 21:33:11,782 - __main__ - INFO -   总种子数: 0
2025-06-22 21:33:11,782 - __main__ - INFO -   处理时间: 0.03秒
2025-06-22 21:33:11,782 - __main__ - WARNING - 失败文件 (5):
2025-06-22 21:33:11,782 - __main__ - WARNING -   单个大种子.png: OpenCV(4.6.0) :-1: error: (-5:Bad argument) in function 'connectedComponentsWithStats'
> Overload resolution failed:
>  - 'dtype' is an invalid keyword argument for connectedComponentsWithStats()
>  - 'dtype' is an invalid keyword argument for connectedComponentsWithStats()

2025-06-22 21:33:11,782 - __main__ - WARNING -   复杂形状种子.png: OpenCV(4.6.0) :-1: error: (-5:Bad argument) in function 'connectedComponentsWithStats'
> Overload resolution failed:
>  - 'dtype' is an invalid keyword argument for connectedComponentsWithStats()
>  - 'dtype' is an invalid keyword argument for connectedComponentsWithStats()

2025-06-22 21:33:11,782 - __main__ - WARNING -   多种子样本.png: OpenCV(4.6.0) :-1: error: (-5:Bad argument) in function 'connectedComponentsWithStats'
> Overload resolution failed:
>  - 'dtype' is an invalid keyword argument for connectedComponentsWithStats()
>  - 'dtype' is an invalid keyword argument for connectedComponentsWithStats()

2025-06-22 21:33:11,782 - __main__ - WARNING -   大小种子混合.png: OpenCV(4.6.0) :-1: error: (-5:Bad argument) in function 'connectedComponentsWithStats'
> Overload resolution failed:
>  - 'dtype' is an invalid keyword argument for connectedComponentsWithStats()
>  - 'dtype' is an invalid keyword argument for connectedComponentsWithStats()

2025-06-22 21:33:11,782 - __main__ - WARNING -   边缘种子.png: OpenCV(4.6.0) :-1: error: (-5:Bad argument) in function 'connectedComponentsWithStats'
> Overload resolution failed:
>  - 'dtype' is an invalid keyword argument for connectedComponentsWithStats()
>  - 'dtype' is an invalid keyword argument for connectedComponentsWithStats()

2025-06-22 21:33:11,782 - __main__ - INFO - 总处理时间: 0.03秒
2025-06-22 21:41:52,980 - __main__ - INFO - 透明背景种子分割系统配置:
2025-06-22 21:41:52,981 - __main__ - INFO -   min_seed_area: 100
2025-06-22 21:41:52,981 - __main__ - INFO -   max_seed_area: 100000
2025-06-22 21:41:52,981 - __main__ - INFO -   padding: 10
2025-06-22 21:41:52,981 - __main__ - INFO -   alpha_threshold: 128
2025-06-22 21:41:52,981 - __main__ - INFO -   connectivity: 8
2025-06-22 21:41:52,981 - __main__ - INFO -   morphology_kernel_size: 3
2025-06-22 21:41:52,981 - __main__ - INFO -   min_alpha_ratio: 0.1
2025-06-22 21:41:52,981 - __main__ - INFO -   output_format: png
2025-06-22 21:41:52,981 - __main__ - INFO -   preserve_quality: True
2025-06-22 21:41:52,981 - __main__ - INFO -   remove_noise: True
2025-06-22 21:41:52,981 - __main__ - INFO - 初始化透明背景种子分割系统...
2025-06-22 21:41:52,981 - transparent_seed_segmentation - INFO - 透明背景种子分割系统初始化完成
2025-06-22 21:41:52,981 - __main__ - INFO - 批量处理透明背景图像:
2025-06-22 21:41:52,981 - __main__ - INFO -   输入目录: transparent_demo
2025-06-22 21:41:52,982 - __main__ - INFO -   输出目录: transparent_demo_results
2025-06-22 21:41:52,982 - __main__ - INFO -   支持格式: ['.png']
2025-06-22 21:41:52,982 - transparent_seed_segmentation - INFO - 找到 5 个图像文件进行批量处理
2025-06-22 21:41:52,982 - transparent_seed_segmentation - INFO - 处理文件 1/5: 单个大种子.png
2025-06-22 21:41:52,982 - transparent_seed_segmentation - INFO - 读取透明背景图像: transparent_demo\单个大种子.png
2025-06-22 21:41:52,990 - PIL.PngImagePlugin - DEBUG - STREAM b'IHDR' 16 13
2025-06-22 21:41:52,991 - PIL.PngImagePlugin - DEBUG - STREAM b'IDAT' 41 2299
2025-06-22 21:41:52,995 - transparent_seed_segmentation - INFO - 分析alpha通道...
2025-06-22 21:41:52,995 - transparent_seed_segmentation - INFO - 查找种子组件...
2025-06-22 21:41:52,997 - transparent_seed_segmentation - INFO - 找到 1 个有效种子组件
2025-06-22 21:41:52,998 - transparent_seed_segmentation - DEBUG - 处理种子 1/1
2025-06-22 21:41:53,006 - transparent_seed_segmentation - INFO -   ✓ 成功提取 1 个种子
2025-06-22 21:41:53,006 - transparent_seed_segmentation - INFO - 处理文件 2/5: 复杂形状种子.png
2025-06-22 21:41:53,007 - transparent_seed_segmentation - INFO - 读取透明背景图像: transparent_demo\复杂形状种子.png
2025-06-22 21:41:53,007 - PIL.PngImagePlugin - DEBUG - STREAM b'IHDR' 16 13
2025-06-22 21:41:53,007 - PIL.PngImagePlugin - DEBUG - STREAM b'IDAT' 41 2283
2025-06-22 21:41:53,008 - transparent_seed_segmentation - INFO - 分析alpha通道...
2025-06-22 21:41:53,009 - transparent_seed_segmentation - INFO - 查找种子组件...
2025-06-22 21:41:53,010 - transparent_seed_segmentation - INFO - 找到 2 个有效种子组件
2025-06-22 21:41:53,010 - transparent_seed_segmentation - DEBUG - 处理种子 1/2
2025-06-22 21:41:53,011 - transparent_seed_segmentation - DEBUG - 处理种子 2/2
2025-06-22 21:41:53,016 - transparent_seed_segmentation - INFO -   ✓ 成功提取 2 个种子
2025-06-22 21:41:53,016 - transparent_seed_segmentation - INFO - 处理文件 3/5: 多种子样本.png
2025-06-22 21:41:53,016 - transparent_seed_segmentation - INFO - 读取透明背景图像: transparent_demo\多种子样本.png
2025-06-22 21:41:53,017 - PIL.PngImagePlugin - DEBUG - STREAM b'IHDR' 16 13
2025-06-22 21:41:53,017 - PIL.PngImagePlugin - DEBUG - STREAM b'IDAT' 41 2956
2025-06-22 21:41:53,018 - transparent_seed_segmentation - INFO - 分析alpha通道...
2025-06-22 21:41:53,019 - transparent_seed_segmentation - INFO - 查找种子组件...
2025-06-22 21:41:53,020 - transparent_seed_segmentation - INFO - 找到 5 个有效种子组件
2025-06-22 21:41:53,020 - transparent_seed_segmentation - DEBUG - 处理种子 1/5
2025-06-22 21:41:53,021 - transparent_seed_segmentation - DEBUG - 处理种子 2/5
2025-06-22 21:41:53,022 - transparent_seed_segmentation - DEBUG - 处理种子 3/5
2025-06-22 21:41:53,023 - transparent_seed_segmentation - DEBUG - 处理种子 4/5
2025-06-22 21:41:53,023 - transparent_seed_segmentation - DEBUG - 处理种子 5/5
2025-06-22 21:41:53,029 - transparent_seed_segmentation - INFO -   ✓ 成功提取 5 个种子
2025-06-22 21:41:53,029 - transparent_seed_segmentation - INFO - 处理文件 4/5: 大小种子混合.png
2025-06-22 21:41:53,029 - transparent_seed_segmentation - INFO - 读取透明背景图像: transparent_demo\大小种子混合.png
2025-06-22 21:41:53,029 - PIL.PngImagePlugin - DEBUG - STREAM b'IHDR' 16 13
2025-06-22 21:41:53,029 - PIL.PngImagePlugin - DEBUG - STREAM b'IDAT' 41 2374
2025-06-22 21:41:53,031 - transparent_seed_segmentation - INFO - 分析alpha通道...
2025-06-22 21:41:53,031 - transparent_seed_segmentation - INFO - 查找种子组件...
2025-06-22 21:41:53,032 - transparent_seed_segmentation - INFO - 找到 4 个有效种子组件
2025-06-22 21:41:53,032 - transparent_seed_segmentation - DEBUG - 处理种子 1/4
2025-06-22 21:41:53,034 - transparent_seed_segmentation - DEBUG - 处理种子 2/4
2025-06-22 21:41:53,034 - transparent_seed_segmentation - DEBUG - 处理种子 3/4
2025-06-22 21:41:53,036 - transparent_seed_segmentation - DEBUG - 处理种子 4/4
2025-06-22 21:41:53,040 - transparent_seed_segmentation - INFO -   ✓ 成功提取 4 个种子
2025-06-22 21:41:53,040 - transparent_seed_segmentation - INFO - 处理文件 5/5: 边缘种子.png
2025-06-22 21:41:53,040 - transparent_seed_segmentation - INFO - 读取透明背景图像: transparent_demo\边缘种子.png
2025-06-22 21:41:53,040 - PIL.PngImagePlugin - DEBUG - STREAM b'IHDR' 16 13
2025-06-22 21:41:53,040 - PIL.PngImagePlugin - DEBUG - STREAM b'IDAT' 41 2465
2025-06-22 21:41:53,042 - transparent_seed_segmentation - INFO - 分析alpha通道...
2025-06-22 21:41:53,042 - transparent_seed_segmentation - INFO - 查找种子组件...
2025-06-22 21:41:53,044 - transparent_seed_segmentation - INFO - 找到 5 个有效种子组件
2025-06-22 21:41:53,044 - transparent_seed_segmentation - DEBUG - 处理种子 1/5
2025-06-22 21:41:53,044 - transparent_seed_segmentation - DEBUG - 处理种子 2/5
2025-06-22 21:41:53,045 - transparent_seed_segmentation - DEBUG - 处理种子 3/5
2025-06-22 21:41:53,046 - transparent_seed_segmentation - DEBUG - 处理种子 4/5
2025-06-22 21:41:53,047 - transparent_seed_segmentation - DEBUG - 处理种子 5/5
2025-06-22 21:41:53,051 - transparent_seed_segmentation - INFO -   ✓ 成功提取 5 个种子
2025-06-22 21:41:53,051 - __main__ - INFO - 批量处理完成!
2025-06-22 21:41:53,051 - __main__ - INFO -   处理文件: 5/5
2025-06-22 21:41:53,051 - __main__ - INFO -   总种子数: 17
2025-06-22 21:41:53,051 - __main__ - INFO -   处理时间: 0.07秒
2025-06-22 21:41:53,051 - __main__ - INFO - 总处理时间: 0.07秒
