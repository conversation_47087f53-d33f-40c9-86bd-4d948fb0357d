#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
小屏幕GUI测试脚本
测试改进后的GPU加速SAM Everything GUI在不同屏幕尺寸下的表现
"""

import tkinter as tk
from tkinter import ttk, messagebox
import sys
import os

def test_screen_sizes():
    """测试不同屏幕尺寸下的GUI表现"""
    
    # 常见的笔记本屏幕分辨率
    screen_sizes = [
        ("1366x768", 1366, 768),
        ("1920x1080", 1920, 1080),
        ("1440x900", 1440, 900),
        ("1280x720", 1280, 720),
        ("1600x900", 1600, 900),
    ]
    
    def create_test_window(name, width, height):
        """创建测试窗口"""
        test_root = tk.Toplevel()
        test_root.title(f"GUI测试 - {name}")
        test_root.geometry(f"{width}x{height}")
        
        # 模拟主要组件
        main_frame = ttk.Frame(test_root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 左侧滚动区域
        left_container = ttk.Frame(main_frame)
        left_container.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 5))
        
        canvas = tk.Canvas(left_container, width=420, highlightthickness=0)
        scrollbar = ttk.Scrollbar(left_container, orient=tk.VERTICAL, command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)
        
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        canvas.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 添加测试控件
        for i in range(10):
            frame = ttk.LabelFrame(scrollable_frame, text=f"测试区域 {i+1}")
            frame.pack(fill=tk.X, padx=5, pady=5)
            
            for j in range(3):
                ttk.Label(frame, text=f"标签 {j+1}").pack(anchor=tk.W, padx=5)
                ttk.Button(frame, text=f"按钮 {j+1}").pack(fill=tk.X, padx=5, pady=2)
        
        # 右侧图像区域
        image_frame = ttk.LabelFrame(main_frame, text="图像显示区域")
        image_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)
        
        canvas_img = tk.Canvas(image_frame, bg='lightgray')
        canvas_img.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 在图像区域显示分辨率信息
        canvas_img.create_text(
            200, 100, 
            text=f"屏幕分辨率: {name}\n窗口大小: {width}x{height}\n\n滚动左侧面板测试所有控件",
            font=('Arial', 12),
            fill='black'
        )
        
        # 绑定鼠标滚轮
        def _on_mousewheel(event):
            canvas.yview_scroll(int(-1*(event.delta/120)), "units")
        
        def _bind_to_mousewheel(event):
            canvas.bind_all("<MouseWheel>", _on_mousewheel)
        
        def _unbind_from_mousewheel(event):
            canvas.unbind_all("<MouseWheel>")
        
        canvas.bind('<Enter>', _bind_to_mousewheel)
        canvas.bind('<Leave>', _unbind_from_mousewheel)
        
        return test_root
    
    # 主测试窗口
    root = tk.Tk()
    root.title("小屏幕GUI适配测试")
    root.geometry("600x400")
    
    ttk.Label(root, text="小屏幕GUI适配测试", font=('Arial', 16, 'bold')).pack(pady=20)
    
    ttk.Label(root, text="点击下面的按钮测试不同屏幕分辨率下的GUI表现:", 
              font=('Arial', 10)).pack(pady=10)
    
    button_frame = ttk.Frame(root)
    button_frame.pack(pady=20)
    
    for name, width, height in screen_sizes:
        btn = ttk.Button(button_frame, text=f"测试 {name}", 
                        command=lambda n=name, w=width, h=height: create_test_window(n, w, h))
        btn.pack(pady=5, fill=tk.X)
    
    ttk.Separator(root, orient=tk.HORIZONTAL).pack(fill=tk.X, pady=20)
    
    # 启动实际GUI的按钮
    def launch_actual_gui():
        """启动实际的GPU加速GUI"""
        try:
            import subprocess
            subprocess.Popen([sys.executable, "sam_everything_gpu_gui_fixed.py"])
            messagebox.showinfo("启动", "GPU加速GUI已启动!")
        except Exception as e:
            messagebox.showerror("错误", f"启动GUI失败: {e}")
    
    ttk.Button(root, text="🚀 启动实际的GPU加速GUI", 
              command=launch_actual_gui).pack(pady=10)
    
    ttk.Label(root, text="注意: 实际GUI支持滚动、缩放和自适应布局", 
              font=('Arial', 9), foreground='blue').pack(pady=5)
    
    root.mainloop()

def test_scrolling_performance():
    """测试滚动性能"""
    root = tk.Tk()
    root.title("滚动性能测试")
    root.geometry("800x600")
    
    # 创建大量内容的滚动区域
    main_frame = ttk.Frame(root)
    main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
    
    canvas = tk.Canvas(main_frame, highlightthickness=0)
    scrollbar = ttk.Scrollbar(main_frame, orient=tk.VERTICAL, command=canvas.yview)
    scrollable_frame = ttk.Frame(canvas)
    
    scrollable_frame.bind(
        "<Configure>",
        lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
    )
    
    canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
    canvas.configure(yscrollcommand=scrollbar.set)
    
    canvas.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
    scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    # 添加大量控件测试滚动性能
    for i in range(50):
        frame = ttk.LabelFrame(scrollable_frame, text=f"性能测试区域 {i+1}")
        frame.pack(fill=tk.X, padx=5, pady=2)
        
        for j in range(5):
            control_frame = ttk.Frame(frame)
            control_frame.pack(fill=tk.X, padx=5, pady=1)
            
            ttk.Label(control_frame, text=f"控件 {i+1}-{j+1}:").pack(side=tk.LEFT)
            ttk.Entry(control_frame, width=20).pack(side=tk.LEFT, padx=5)
            ttk.Button(control_frame, text="测试").pack(side=tk.LEFT)
    
    # 绑定鼠标滚轮
    def _on_mousewheel(event):
        canvas.yview_scroll(int(-1*(event.delta/120)), "units")
    
    canvas.bind_all("<MouseWheel>", _on_mousewheel)
    
    ttk.Label(root, text="使用鼠标滚轮测试滚动性能", 
              font=('Arial', 10, 'bold')).pack(side=tk.BOTTOM, pady=5)
    
    root.mainloop()

def main():
    """主函数"""
    if len(sys.argv) > 1 and sys.argv[1] == "--performance":
        test_scrolling_performance()
    else:
        test_screen_sizes()

if __name__ == "__main__":
    main()
