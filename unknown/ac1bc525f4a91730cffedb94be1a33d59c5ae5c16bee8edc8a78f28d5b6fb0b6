# GPU-accelerated SAM Everything (SAME) Requirements
# Compatible with CUDA 11.2

# Core PyTorch with CUDA 11.2 support
torch==1.12.1+cu112
torchvision==0.13.1+cu112
torchaudio==0.12.1+cu112
--extra-index-url https://download.pytorch.org/whl/cu112

# Segment Anything Model
segment-anything==1.0

# Computer Vision and Image Processing
opencv-python==********
Pillow==10.0.1
numpy==1.24.3

# GUI Framework
tkinter-tooltip==2.1.0

# Utility Libraries
typing-extensions==4.7.1
matplotlib==3.7.2

# Optional: For better performance
scikit-image==0.21.0

# Development and Testing (optional)
pytest==7.4.2
black==23.7.0
flake8==6.0.0

# Note: tkinter is usually included with Python installation
# If you encounter tkinter issues, install python3-tk on Linux:
# sudo apt-get install python3-tk
