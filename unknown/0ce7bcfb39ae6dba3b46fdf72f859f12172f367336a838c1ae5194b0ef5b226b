#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
YOLO集成功能演示
展示完整的从图像分割到YOLO训练和识别的工作流程
"""

import os
import sys
import time
import shutil
import tempfile
from pathlib import Path

def check_dependencies():
    """检查依赖是否安装"""
    print("🔍 检查依赖...")
    
    missing_deps = []
    
    try:
        import cv2
        print("✅ OpenCV 可用")
    except ImportError:
        missing_deps.append("opencv-python")
    
    try:
        from PIL import Image
        print("✅ PIL 可用")
    except ImportError:
        missing_deps.append("Pillow")
    
    try:
        from ultralytics import YOLO
        print("✅ Ultralytics 可用")
    except ImportError:
        missing_deps.append("ultralytics")
    
    try:
        from transparent_seed_segmentation import TransparentSeedSegmentation
        print("✅ 透明背景分割 可用")
    except ImportError:
        missing_deps.append("透明背景分割模块")
    
    try:
        from yolo_manager import YOLOManager
        print("✅ YOLO管理器 可用")
    except ImportError:
        missing_deps.append("YOLO管理器模块")
    
    if missing_deps:
        print(f"\n❌ 缺少依赖: {', '.join(missing_deps)}")
        print("请运行: python install_yolo_dependencies.py")
        return False
    
    print("✅ 所有依赖都已安装")
    return True

def create_demo_workflow():
    """创建完整的演示工作流程"""
    print("\n🚀 YOLO集成功能演示")
    print("=" * 60)
    
    if not check_dependencies():
        return False
    
    # 创建演示目录
    demo_dir = "yolo_integration_demo"
    if os.path.exists(demo_dir):
        shutil.rmtree(demo_dir)
    os.makedirs(demo_dir)
    
    print(f"\n📁 演示目录: {os.path.abspath(demo_dir)}")
    
    # 步骤1: 创建演示图像
    print(f"\n1️⃣ 创建演示图像...")
    demo_images = create_demo_images(demo_dir)
    
    # 步骤2: 图像分割
    print(f"\n2️⃣ 执行图像分割...")
    segmentation_results = perform_segmentation(demo_images, demo_dir)
    
    # 步骤3: 创建YOLO数据集
    print(f"\n3️⃣ 创建YOLO训练数据集...")
    dataset_info = create_yolo_dataset(segmentation_results, demo_dir)
    
    # 步骤4: 演示YOLO模型管理
    print(f"\n4️⃣ 演示YOLO模型管理...")
    model_demo = demonstrate_model_management(demo_dir)
    
    # 步骤5: 演示训练流程（不实际训练）
    print(f"\n5️⃣ 演示训练流程...")
    training_demo = demonstrate_training_workflow(dataset_info, demo_dir)
    
    # 步骤6: 演示预测功能
    print(f"\n6️⃣ 演示预测功能...")
    prediction_demo = demonstrate_prediction_workflow(demo_images, demo_dir)
    
    # 总结
    print(f"\n🎯 演示总结")
    print("=" * 30)
    print(f"✅ 演示图像: {len(demo_images)} 张")
    print(f"✅ 分割结果: {len(segmentation_results)} 个")
    print(f"✅ YOLO数据集: {'已创建' if dataset_info else '创建失败'}")
    print(f"✅ 模型管理: {'正常' if model_demo else '异常'}")
    print(f"✅ 训练流程: {'演示完成' if training_demo else '演示失败'}")
    print(f"✅ 预测功能: {'演示完成' if prediction_demo else '演示失败'}")
    
    print(f"\n📋 下一步操作:")
    print(f"1. 启动GUI: python enhanced_segmentation_gui.py")
    print(f"2. 切换到 'YOLO训练与识别' 标签页")
    print(f"3. 在模型管理页面下载模型")
    print(f"4. 在训练页面使用演示数据集训练")
    print(f"5. 在识别页面测试训练好的模型")
    
    return True

def create_demo_images(demo_dir):
    """创建演示图像"""
    import cv2
    import numpy as np
    from PIL import Image
    
    images_dir = os.path.join(demo_dir, "demo_images")
    os.makedirs(images_dir, exist_ok=True)
    
    demo_images = []
    
    # 创建不同类型的种子图像
    seed_configs = [
        {
            'filename': '小麦种子_5个.png',
            'seeds': [
                {'center': (100, 80), 'size': (20, 35), 'color': [200, 180, 120, 255]},
                {'center': (200, 100), 'size': (22, 38), 'color': [210, 190, 130, 255]},
                {'center': (150, 180), 'size': (21, 36), 'color': [190, 170, 110, 255]},
                {'center': (280, 160), 'size': (23, 40), 'color': [205, 185, 125, 255]},
                {'center': (80, 220), 'size': (19, 34), 'color': [195, 175, 115, 255]},
            ]
        },
        {
            'filename': '玉米种子_3个.png',
            'seeds': [
                {'center': (120, 120), 'size': (35, 50), 'color': [220, 200, 80, 255]},
                {'center': (250, 140), 'size': (38, 55), 'color': [230, 210, 90, 255]},
                {'center': (180, 240), 'size': (36, 52), 'color': [225, 205, 85, 255]},
            ]
        },
        {
            'filename': '大豆种子_4个.png',
            'seeds': [
                {'center': (90, 90), 'size': (30, 30), 'color': [180, 160, 100, 255]},
                {'center': (200, 110), 'size': (32, 32), 'color': [185, 165, 105, 255]},
                {'center': (150, 200), 'size': (31, 31), 'color': [175, 155, 95, 255]},
                {'center': (260, 220), 'size': (33, 33), 'color': [190, 170, 110, 255]},
            ]
        }
    ]
    
    for config in seed_configs:
        # 创建透明背景图像
        image = np.zeros((300, 350, 4), dtype=np.uint8)
        
        for seed in config['seeds']:
            mask = np.zeros((300, 350), dtype=np.uint8)
            cv2.ellipse(mask, seed['center'], seed['size'], 0, 0, 360, 255, -1)
            image[mask > 0] = seed['color']
        
        # 保存图像
        file_path = os.path.join(images_dir, config['filename'])
        pil_image = Image.fromarray(image, 'RGBA')
        pil_image.save(file_path, 'PNG')
        
        demo_images.append({
            'path': file_path,
            'filename': config['filename'],
            'expected_seeds': len(config['seeds'])
        })
        
        print(f"  创建: {config['filename']} ({len(config['seeds'])} 个种子)")
    
    return demo_images

def perform_segmentation(demo_images, demo_dir):
    """执行图像分割"""
    from transparent_seed_segmentation import TransparentSeedSegmentation
    
    results_dir = os.path.join(demo_dir, "segmentation_results")
    
    config = {
        'alpha_threshold': 50,
        'min_seed_area': 200,
        'max_seed_area': 50000,
        'padding': 15,
        'remove_noise': False,
    }
    
    segmentation = TransparentSeedSegmentation(config)
    segmentation_results = []
    
    for image_info in demo_images:
        print(f"  处理: {image_info['filename']}")
        
        result = segmentation.process_transparent_image(
            image_info['path'], results_dir
        )
        
        if result['success']:
            print(f"    ✅ 提取 {result['seeds_count']} 个种子")
            segmentation_results.append(result)
        else:
            print(f"    ❌ 处理失败: {result.get('error', '未知错误')}")
    
    return segmentation_results

def create_yolo_dataset(segmentation_results, demo_dir):
    """创建YOLO数据集"""
    try:
        from yolo_format_converter import YOLOFormatConverter
        
        converter = YOLOFormatConverter()
        source_dir = os.path.join(demo_dir, "segmentation_results")
        dataset_dir = os.path.join(demo_dir, "yolo_dataset")
        
        dataset_info = converter.create_yolo_dataset(
            source_dir=source_dir,
            output_dir=dataset_dir,
            train_ratio=0.7
        )
        
        if dataset_info:
            print(f"  ✅ 数据集创建成功")
            print(f"    训练图像: {dataset_info['train_images']}")
            print(f"    验证图像: {dataset_info['val_images']}")
            print(f"    配置文件: {dataset_info['config_file']}")
            return dataset_info
        else:
            print(f"  ❌ 数据集创建失败")
            return None
            
    except Exception as e:
        print(f"  ❌ 创建数据集时出错: {e}")
        return None

def demonstrate_model_management(demo_dir):
    """演示模型管理"""
    try:
        from yolo_manager import YOLOManager
        
        manager = YOLOManager(os.path.join(demo_dir, "yolo_models"))
        
        # 获取可用模型
        models = manager.get_available_models()
        print(f"  📦 可用模型: {len(models)} 个")
        
        # 显示几个推荐模型
        recommended = ['yolov8n', 'yolov8s', 'yolov11n']
        for model_id in recommended:
            if model_id in models:
                model_info = models[model_id]
                status = "已下载" if model_info['downloaded'] else "需下载"
                print(f"    {model_id}: {model_info['name']} - {model_info['size']} ({status})")
        
        print(f"  💡 提示: 在GUI中可以一键下载和管理这些模型")
        return True
        
    except Exception as e:
        print(f"  ❌ 模型管理演示失败: {e}")
        return False

def demonstrate_training_workflow(dataset_info, demo_dir):
    """演示训练工作流程"""
    if not dataset_info:
        print(f"  ⚠️ 无数据集，跳过训练演示")
        return False
    
    print(f"  📚 训练数据集已准备:")
    print(f"    配置文件: {dataset_info['config_file']}")
    print(f"    训练样本: {dataset_info['train_images']} 张")
    print(f"    验证样本: {dataset_info['val_images']} 张")
    
    print(f"  🏋️ 推荐训练参数:")
    print(f"    基础模型: yolov8n (快速训练)")
    print(f"    训练轮数: 50-100")
    print(f"    批次大小: 8-16")
    print(f"    图像尺寸: 640")
    
    print(f"  💡 在GUI中可以:")
    print(f"    - 选择预训练模型")
    print(f"    - 调整训练参数")
    print(f"    - 实时查看训练进度")
    print(f"    - 自动保存最佳模型")
    
    return True

def demonstrate_prediction_workflow(demo_images, demo_dir):
    """演示预测工作流程"""
    print(f"  🔍 预测功能演示:")
    print(f"    测试图像: {len(demo_images)} 张")
    
    print(f"  🎯 预测流程:")
    print(f"    1. 选择训练好的模型")
    print(f"    2. 设置置信度阈值 (推荐: 0.5)")
    print(f"    3. 选择要预测的图像")
    print(f"    4. 查看检测结果")
    
    print(f"  📊 输出结果包括:")
    print(f"    - 检测到的种子数量")
    print(f"    - 每个种子的位置和置信度")
    print(f"    - 带标注的结果图像")
    print(f"    - 详细的检测统计")
    
    print(f"  💡 支持批量预测整个文件夹")
    
    return True

def main():
    """主函数"""
    try:
        success = create_demo_workflow()
        
        if success:
            print(f"\n🎉 YOLO集成功能演示完成！")
            print(f"\n🚀 现在启动GUI体验完整功能:")
            print(f"python enhanced_segmentation_gui.py")
        else:
            print(f"\n❌ 演示过程中出现问题")
            return 1
            
    except KeyboardInterrupt:
        print(f"\n⏹️ 演示被用户中断")
        return 1
    except Exception as e:
        print(f"\n💥 演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
