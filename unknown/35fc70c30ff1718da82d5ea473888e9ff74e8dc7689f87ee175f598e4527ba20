#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试JSON序列化修复
验证NumPy类型转换是否正常工作
"""

import os
import sys
import cv2
import numpy as np
import tempfile
import shutil
from PIL import Image

def create_test_image():
    """创建测试用的透明背景图像"""
    print("创建测试图像...")
    
    # 创建RGBA图像
    image = np.zeros((200, 300, 4), dtype=np.uint8)
    
    # 添加两个种子
    cv2.circle(image, (100, 80), 25, [200, 150, 100, 255], -1)
    cv2.circle(image, (200, 120), 30, [150, 200, 120, 255], -1)
    
    return image

def test_transparent_segmentation_json():
    """测试透明背景分割的JSON生成"""
    print("\n🧪 测试透明背景分割JSON生成...")
    
    try:
        from transparent_seed_segmentation import TransparentSeedSegmentation
        
        # 创建临时目录
        temp_dir = tempfile.mkdtemp()
        
        try:
            # 创建测试图像
            test_image = create_test_image()
            test_path = os.path.join(temp_dir, "test_seeds.png")
            
            # 保存为PNG
            pil_image = Image.fromarray(test_image, 'RGBA')
            pil_image.save(test_path, 'PNG')
            
            # 配置分割系统
            config = {
                'alpha_threshold': 50,
                'min_seed_area': 100,
                'max_seed_area': 10000,
                'padding': 10,
                'remove_noise': False,
            }
            
            segmentation = TransparentSeedSegmentation(config)
            
            # 处理图像
            result = segmentation.process_transparent_image(test_path, temp_dir)
            
            if result['success']:
                print(f"✅ 分割成功，提取 {result['seeds_count']} 个种子")
                
                # 检查YOLO JSON文件
                if 'yolo_json_path' in result and result['yolo_json_path']:
                    json_path = result['yolo_json_path']
                    if os.path.exists(json_path):
                        print(f"✅ YOLO JSON文件已生成: {os.path.basename(json_path)}")
                        
                        # 验证JSON文件内容
                        import json
                        with open(json_path, 'r', encoding='utf-8') as f:
                            json_data = json.load(f)
                        
                        print(f"✅ JSON文件读取成功")
                        print(f"   图像尺寸: {json_data['imageWidth']} x {json_data['imageHeight']}")
                        print(f"   标注数量: {len(json_data['shapes'])}")
                        
                        # 检查数据类型
                        for i, shape in enumerate(json_data['shapes']):
                            points = shape['points']
                            print(f"   种子 {i+1}: 坐标类型 {type(points[0][0])}")
                            
                            # 验证所有坐标都是Python原生类型
                            for point in points:
                                assert isinstance(point[0], int), f"X坐标不是int类型: {type(point[0])}"
                                assert isinstance(point[1], int), f"Y坐标不是int类型: {type(point[1])}"
                        
                        print(f"✅ 所有坐标类型验证通过")
                        return True
                    else:
                        print(f"❌ YOLO JSON文件不存在")
                        return False
                else:
                    print(f"❌ 结果中没有YOLO JSON路径")
                    return False
            else:
                print(f"❌ 分割失败: {result.get('error', '未知错误')}")
                return False
                
        finally:
            # 清理临时文件
            shutil.rmtree(temp_dir, ignore_errors=True)
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_numpy_conversion():
    """测试NumPy类型转换函数"""
    print("\n🧪 测试NumPy类型转换...")
    
    try:
        from transparent_seed_segmentation import convert_numpy_types
        
        # 测试各种NumPy类型
        test_data = {
            'int32': np.int32(42),
            'int64': np.int64(123),
            'float32': np.float32(3.14),
            'float64': np.float64(2.718),
            'array': np.array([1, 2, 3]),
            'nested': {
                'bbox': np.array([10, 20, 30, 40]),
                'area': np.int32(1200),
                'confidence': np.float32(0.85)
            },
            'list_with_numpy': [np.int32(1), np.float64(2.5), np.array([3, 4])]
        }
        
        # 转换
        converted = convert_numpy_types(test_data)
        
        # 验证转换结果
        assert isinstance(converted['int32'], int), "int32转换失败"
        assert isinstance(converted['int64'], int), "int64转换失败"
        assert isinstance(converted['float32'], float), "float32转换失败"
        assert isinstance(converted['float64'], float), "float64转换失败"
        assert isinstance(converted['array'], list), "array转换失败"
        assert isinstance(converted['nested']['area'], int), "嵌套int32转换失败"
        assert isinstance(converted['list_with_numpy'][0], int), "列表中int32转换失败"
        
        print("✅ NumPy类型转换测试通过")
        
        # 测试JSON序列化
        import json
        json_str = json.dumps(converted, indent=2)
        print("✅ JSON序列化测试通过")
        
        return True
        
    except Exception as e:
        print(f"❌ NumPy转换测试失败: {e}")
        return False

def test_json_serialization_edge_cases():
    """测试JSON序列化边界情况"""
    print("\n🧪 测试JSON序列化边界情况...")
    
    try:
        from transparent_seed_segmentation import convert_numpy_types
        import json
        
        # 测试边界情况
        edge_cases = [
            # 空数据
            {},
            [],
            None,
            
            # 大数值
            {'big_int': np.int64(9223372036854775807)},
            {'big_float': np.float64(1.7976931348623157e+308)},
            
            # 特殊值
            {'nan': np.nan},
            {'inf': np.inf},
            {'neg_inf': -np.inf},
            
            # 复杂嵌套
            {
                'level1': {
                    'level2': {
                        'level3': [np.int32(1), np.float64(2.0), np.array([3, 4, 5])]
                    }
                }
            }
        ]
        
        for i, case in enumerate(edge_cases):
            try:
                converted = convert_numpy_types(case)
                json_str = json.dumps(converted)
                print(f"✅ 边界情况 {i+1} 通过")
            except Exception as e:
                print(f"⚠️ 边界情况 {i+1} 失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 边界情况测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🔧 JSON序列化修复测试")
    print("=" * 50)
    
    tests = [
        ("NumPy类型转换", test_numpy_conversion),
        ("JSON序列化边界情况", test_json_serialization_edge_cases),
        ("透明背景分割JSON生成", test_transparent_segmentation_json),
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        print(f"\n🧪 {test_name}")
        print("-" * 30)
        
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 通过")
            else:
                failed += 1
                print(f"❌ {test_name} 失败")
        except Exception as e:
            failed += 1
            print(f"💥 {test_name} 异常: {e}")
    
    # 总结
    print(f"\n" + "=" * 50)
    print(f"测试总结:")
    print(f"✅ 通过: {passed}")
    print(f"❌ 失败: {failed}")
    print(f"📊 总计: {passed + failed}")
    
    if failed == 0:
        print(f"\n🎉 所有测试通过！JSON序列化问题已修复。")
        print(f"\n现在可以正常使用YOLO功能:")
        print(f"python enhanced_segmentation_gui.py")
        return 0
    else:
        print(f"\n💥 {failed} 个测试失败！")
        print(f"请检查修复是否正确")
        return 1

if __name__ == "__main__":
    sys.exit(main())
