#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
透明背景种子分割系统演示
展示系统功能和使用方法
"""

import os
import sys
import cv2
import numpy as np
import tempfile
import shutil
from PIL import Image

def create_comprehensive_demo():
    """创建全面的演示环境"""
    print("🌱 透明背景种子分割系统演示")
    print("=" * 50)
    
    # 创建演示目录
    demo_dir = "transparent_demo"
    if os.path.exists(demo_dir):
        shutil.rmtree(demo_dir)
    os.makedirs(demo_dir)
    
    # 创建不同类型的演示图像
    demo_images = create_demo_images(demo_dir)
    
    print(f"\n📁 演示文件已创建在: {os.path.abspath(demo_dir)}")
    print(f"包含 {len(demo_images)} 个演示图像文件")
    
    return demo_dir, demo_images

def create_demo_images(demo_dir):
    """创建各种类型的演示图像"""
    demo_configs = [
        {
            'filename': '多种子样本.png',
            'description': '包含5个不同形状和大小的种子',
            'seeds': [
                {'type': 'ellipse', 'center': (150, 120), 'size': (40, 60), 'color': [180, 100, 50, 255]},
                {'type': 'circle', 'center': (350, 150), 'size': 35, 'color': [50, 180, 100, 255]},
                {'type': 'ellipse', 'center': (250, 300), 'size': (55, 35), 'color': [100, 50, 180, 255]},
                {'type': 'circle', 'center': (450, 280), 'size': 28, 'color': [200, 150, 80, 255]},
                {'type': 'ellipse', 'center': (100, 350), 'size': (30, 45), 'color': [80, 200, 150, 255]},
            ]
        },
        {
            'filename': '大小种子混合.png',
            'description': '包含不同大小的种子，测试尺寸过滤',
            'seeds': [
                {'type': 'circle', 'center': (200, 150), 'size': 60, 'color': [200, 100, 100, 255]},  # 大
                {'type': 'circle', 'center': (400, 150), 'size': 25, 'color': [100, 200, 100, 255]},  # 中
                {'type': 'circle', 'center': (300, 300), 'size': 10, 'color': [100, 100, 200, 255]},  # 小（可能被过滤）
                {'type': 'ellipse', 'center': (150, 300), 'size': (80, 40), 'color': [200, 200, 100, 255]},  # 长椭圆
            ]
        },
        {
            'filename': '复杂形状种子.png',
            'description': '包含复杂形状和重叠区域',
            'seeds': [
                {'type': 'ellipse', 'center': (200, 200), 'size': (50, 80), 'color': [150, 100, 200, 255]},
                {'type': 'ellipse', 'center': (250, 220), 'size': (60, 40), 'color': [200, 150, 100, 255]},
                {'type': 'circle', 'center': (400, 150), 'size': 40, 'color': [100, 200, 150, 255]},
            ]
        },
        {
            'filename': '边缘种子.png',
            'description': '种子位于图像边缘，测试边界处理',
            'seeds': [
                {'type': 'circle', 'center': (30, 30), 'size': 25, 'color': [200, 100, 150, 255]},      # 左上角
                {'type': 'circle', 'center': (470, 30), 'size': 25, 'color': [150, 200, 100, 255]},     # 右上角
                {'type': 'circle', 'center': (30, 370), 'size': 25, 'color': [100, 150, 200, 255]},     # 左下角
                {'type': 'circle', 'center': (470, 370), 'size': 25, 'color': [200, 200, 100, 255]},    # 右下角
                {'type': 'ellipse', 'center': (250, 200), 'size': (40, 60), 'color': [150, 150, 150, 255]}, # 中心
            ]
        },
        {
            'filename': '单个大种子.png',
            'description': '单个大型种子，测试单对象处理',
            'seeds': [
                {'type': 'ellipse', 'center': (250, 200), 'size': (100, 150), 'color': [180, 120, 80, 255]},
            ]
        }
    ]
    
    created_images = []
    
    for config in demo_configs:
        print(f"创建: {config['filename']} - {config['description']}")
        
        # 创建透明背景图像 (500x400)
        image = np.zeros((400, 500, 4), dtype=np.uint8)
        
        # 添加种子
        for seed in config['seeds']:
            mask = np.zeros((400, 500), dtype=np.uint8)
            
            if seed['type'] == 'circle':
                cv2.circle(mask, seed['center'], seed['size'], 255, -1)
            elif seed['type'] == 'ellipse':
                cv2.ellipse(mask, seed['center'], seed['size'], 0, 0, 360, 255, -1)
            
            # 应用颜色到种子区域
            image[mask > 0] = seed['color']
        
        # 保存图像
        file_path = os.path.join(demo_dir, config['filename'])
        pil_image = Image.fromarray(image, 'RGBA')
        pil_image.save(file_path, 'PNG')
        
        created_images.append({
            'path': file_path,
            'filename': config['filename'],
            'description': config['description'],
            'seed_count': len(config['seeds'])
        })
    
    return created_images

def demonstrate_cli_usage(demo_dir):
    """演示命令行工具使用"""
    print("\n🖥️  命令行工具使用演示")
    print("-" * 30)
    
    print("基本命令:")
    print(f"python transparent_seed_cli.py --input {demo_dir} --output transparent_results --batch")
    
    print("\n高级参数命令:")
    print(f"python transparent_seed_cli.py \\")
    print(f"    --input {demo_dir} \\")
    print(f"    --output transparent_results \\")
    print(f"    --batch \\")
    print(f"    --min-area 150 \\")
    print(f"    --max-area 80000 \\")
    print(f"    --padding 15 \\")
    print(f"    --alpha-threshold 100 \\")
    print(f"    --verbose")

def demonstrate_gui_usage():
    """演示GUI使用方法"""
    print("\n🖼️  图形界面使用演示")
    print("-" * 30)
    
    print("启动增强GUI:")
    print("python enhanced_segmentation_gui.py")
    
    print("\n使用步骤:")
    print("1. 选择 '透明背景分割 (PNG种子)' 方法")
    print("2. 点击 '选择文件夹' 并选择 transparent_demo 目录")
    print("3. 调整参数（可选）:")
    print("   - 最小种子面积: 100-2000")
    print("   - 最大种子面积: 10000-200000")
    print("   - 裁剪边距: 0-50")
    print("   - 透明度阈值: 0-255")
    print("4. 点击 '批量处理全部' 开始处理")

def demonstrate_python_api():
    """演示Python API使用"""
    print("\n🐍 Python API 使用演示")
    print("-" * 30)
    
    api_code = '''
from transparent_seed_segmentation import TransparentSeedSegmentation

# 配置参数
config = {
    'min_seed_area': 100,
    'max_seed_area': 100000,
    'padding': 10,
    'alpha_threshold': 128,
    'remove_noise': True,
}

# 初始化系统
segmentation = TransparentSeedSegmentation(config)

# 处理单张图像
result = segmentation.process_transparent_image(
    "transparent_demo/多种子样本.png",
    "api_results"
)

if result['success']:
    print(f"成功提取 {result['seeds_count']} 个种子")
    for seed in result['seeds']:
        print(f"  {seed['filename']}: {seed['size']}")
'''
    
    print(api_code)

def show_expected_results(demo_images):
    """显示预期结果"""
    print("\n📊 预期处理结果")
    print("-" * 30)
    
    total_seeds = 0
    for img in demo_images:
        expected_seeds = img['seed_count']
        total_seeds += expected_seeds
        print(f"{img['filename']}: 预期提取 {expected_seeds} 个种子")
        print(f"  描述: {img['description']}")
    
    print(f"\n总计预期种子数: {total_seeds}")
    
    print("\n输出文件结构:")
    print("transparent_results/")
    for img in demo_images:
        base_name = os.path.splitext(img['filename'])[0]
        print(f"├── {base_name}/")
        for i in range(1, img['seed_count'] + 1):
            print(f"│   ├── {base_name}_seed_{i:03d}.png")
        print(f"│   └── {base_name}_transparent_segmentation.jpg")

def show_troubleshooting_tips():
    """显示故障排除提示"""
    print("\n🔧 故障排除提示")
    print("-" * 30)
    
    tips = [
        "如果种子数量不符合预期，尝试调整 alpha_threshold 参数",
        "如果检测到太多小对象，增加 min_seed_area 值",
        "如果大种子被忽略，检查 max_seed_area 设置",
        "如果边界不准确，调整 padding 值获得更好的裁剪效果",
        "启用 verbose 模式查看详细处理信息",
        "确保输入图像是PNG格式且包含透明背景",
    ]
    
    for i, tip in enumerate(tips, 1):
        print(f"{i}. {tip}")

def main():
    """主演示函数"""
    try:
        # 创建演示环境
        demo_dir, demo_images = create_comprehensive_demo()
        
        # 演示各种使用方法
        demonstrate_cli_usage(demo_dir)
        demonstrate_gui_usage()
        demonstrate_python_api()
        
        # 显示预期结果
        show_expected_results(demo_images)
        
        # 故障排除提示
        show_troubleshooting_tips()
        
        print("\n🎯 下一步操作")
        print("-" * 30)
        print("1. 运行测试: python test_transparent_segmentation.py")
        print("2. 创建更多演示图像: python test_transparent_segmentation.py create-demo")
        print("3. 启动GUI: python enhanced_segmentation_gui.py")
        print("4. 处理演示图像: python transparent_seed_cli.py --input transparent_demo --output results --batch")
        
        # 询问是否立即测试
        try:
            response = input("\n是否现在运行命令行处理演示? (y/n): ").lower().strip()
            if response in ['y', 'yes', '是']:
                print("\n正在运行演示处理...")
                import subprocess
                cmd = [
                    sys.executable, 'transparent_seed_cli.py',
                    '--input', demo_dir,
                    '--output', 'transparent_demo_results',
                    '--batch', '--verbose'
                ]
                subprocess.run(cmd)
                print("演示处理完成！查看 transparent_demo_results 目录中的结果。")
        except KeyboardInterrupt:
            print("\n演示结束。")
        
    except Exception as e:
        print(f"演示过程中出现错误: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
