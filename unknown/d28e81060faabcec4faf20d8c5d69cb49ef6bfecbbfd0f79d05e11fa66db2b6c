#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
YOLO格式转换工具
将透明背景分割结果转换为YOLO训练格式
"""

import os
import json
import cv2
import numpy as np
from typing import Dict, List, Tuple
import shutil

class YOLOFormatConverter:
    """YOLO格式转换器"""
    
    def __init__(self):
        self.class_names = ["seed"]  # 默认类别名称
        self.class_mapping = {"seed": 0}  # 类别到ID的映射
    
    def convert_json_to_yolo_txt(self, json_path: str, output_dir: str) -> bool:
        """
        将JSON标注文件转换为YOLO txt格式
        
        Args:
            json_path: JSON标注文件路径
            output_dir: 输出目录
            
        Returns:
            转换是否成功
        """
        try:
            with open(json_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            image_width = data['imageWidth']
            image_height = data['imageHeight']
            
            # 创建YOLO txt文件
            base_name = os.path.splitext(os.path.basename(data['imagePath']))[0]
            txt_filename = f"{base_name}.txt"
            txt_path = os.path.join(output_dir, txt_filename)
            
            with open(txt_path, 'w') as f:
                for shape in data['shapes']:
                    if shape['shape_type'] == 'polygon' and shape['label'] in self.class_mapping:
                        class_id = self.class_mapping[shape['label']]
                        
                        # 获取边界框坐标
                        points = shape['points']
                        x_coords = [p[0] for p in points]
                        y_coords = [p[1] for p in points]
                        
                        x_min, x_max = min(x_coords), max(x_coords)
                        y_min, y_max = min(y_coords), max(y_coords)
                        
                        # 转换为YOLO格式 (归一化的中心点坐标和宽高)
                        center_x = (x_min + x_max) / 2.0 / image_width
                        center_y = (y_min + y_max) / 2.0 / image_height
                        width = (x_max - x_min) / image_width
                        height = (y_max - y_min) / image_height
                        
                        # 写入YOLO格式: class_id center_x center_y width height
                        f.write(f"{class_id} {center_x:.6f} {center_y:.6f} {width:.6f} {height:.6f}\n")
            
            print(f"✅ YOLO txt文件已生成: {txt_path}")
            return True
            
        except Exception as e:
            print(f"❌ 转换失败: {e}")
            return False
    
    def create_yolo_dataset(self, source_dir: str, output_dir: str, 
                           train_ratio: float = 0.8) -> Dict:
        """
        创建完整的YOLO训练数据集
        
        Args:
            source_dir: 源目录（包含处理结果）
            output_dir: YOLO数据集输出目录
            train_ratio: 训练集比例
            
        Returns:
            数据集信息字典
        """
        try:
            # 创建YOLO数据集目录结构
            dataset_dirs = {
                'images': {
                    'train': os.path.join(output_dir, 'images', 'train'),
                    'val': os.path.join(output_dir, 'images', 'val')
                },
                'labels': {
                    'train': os.path.join(output_dir, 'labels', 'train'),
                    'val': os.path.join(output_dir, 'labels', 'val')
                }
            }
            
            for category in dataset_dirs.values():
                for split_dir in category.values():
                    os.makedirs(split_dir, exist_ok=True)
            
            # 扫描源目录中的JSON文件
            json_files = []
            for root, dirs, files in os.walk(source_dir):
                for file in files:
                    if file.endswith('_yolo_annotations.json'):
                        json_path = os.path.join(root, file)
                        
                        # 查找对应的原始图像
                        base_name = file.replace('_yolo_annotations.json', '')
                        
                        # 查找原始图像文件
                        image_extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff']
                        original_image = None
                        
                        for ext in image_extensions:
                            potential_image = os.path.join(os.path.dirname(json_path), f"{base_name}{ext}")
                            if os.path.exists(potential_image):
                                original_image = potential_image
                                break
                        
                        if original_image:
                            json_files.append((json_path, original_image))
            
            if not json_files:
                print("❌ 未找到JSON标注文件")
                return {}
            
            # 分割训练集和验证集
            np.random.shuffle(json_files)
            split_idx = int(len(json_files) * train_ratio)
            train_files = json_files[:split_idx]
            val_files = json_files[split_idx:]
            
            # 处理训练集
            train_count = self._process_dataset_split(train_files, dataset_dirs, 'train')
            
            # 处理验证集
            val_count = self._process_dataset_split(val_files, dataset_dirs, 'val')
            
            # 创建数据集配置文件
            dataset_config = self._create_dataset_yaml(output_dir, train_count, val_count)
            
            # 创建类别文件
            self._create_classes_file(output_dir)
            
            dataset_info = {
                'dataset_dir': output_dir,
                'train_images': train_count,
                'val_images': val_count,
                'total_images': train_count + val_count,
                'classes': self.class_names,
                'config_file': dataset_config
            }
            
            print(f"✅ YOLO数据集创建完成:")
            print(f"   数据集目录: {output_dir}")
            print(f"   训练图像: {train_count}")
            print(f"   验证图像: {val_count}")
            print(f"   配置文件: {dataset_config}")
            
            return dataset_info
            
        except Exception as e:
            print(f"❌ 创建YOLO数据集失败: {e}")
            return {}
    
    def _process_dataset_split(self, file_pairs: List[Tuple], dataset_dirs: Dict, split: str) -> int:
        """处理数据集分割"""
        count = 0
        
        for json_path, image_path in file_pairs:
            try:
                # 复制图像文件
                image_filename = os.path.basename(image_path)
                dest_image_path = os.path.join(dataset_dirs['images'][split], image_filename)
                shutil.copy2(image_path, dest_image_path)
                
                # 转换标注文件
                base_name = os.path.splitext(image_filename)[0]
                txt_filename = f"{base_name}.txt"
                dest_label_path = os.path.join(dataset_dirs['labels'][split], txt_filename)
                
                # 临时目录用于转换
                temp_dir = os.path.dirname(dest_label_path)
                if self.convert_json_to_yolo_txt(json_path, temp_dir):
                    count += 1
                
            except Exception as e:
                print(f"⚠️ 处理文件失败 {json_path}: {e}")
        
        return count
    
    def _create_dataset_yaml(self, output_dir: str, train_count: int, val_count: int) -> str:
        """创建数据集YAML配置文件"""
        yaml_content = f"""# YOLO数据集配置文件
# 由透明背景种子分割系统自动生成

# 数据集路径
path: {os.path.abspath(output_dir)}
train: images/train
val: images/val

# 类别数量
nc: {len(self.class_names)}

# 类别名称
names: {self.class_names}

# 数据集统计
train_images: {train_count}
val_images: {val_count}
total_images: {train_count + val_count}

# 生成信息
generated_by: "Transparent Seed Segmentation System"
description: "种子检测数据集，用于YOLO训练"
"""
        
        yaml_path = os.path.join(output_dir, 'dataset.yaml')
        with open(yaml_path, 'w', encoding='utf-8') as f:
            f.write(yaml_content)
        
        return yaml_path
    
    def _create_classes_file(self, output_dir: str):
        """创建类别文件"""
        classes_path = os.path.join(output_dir, 'classes.txt')
        with open(classes_path, 'w', encoding='utf-8') as f:
            for class_name in self.class_names:
                f.write(f"{class_name}\n")
    
    def add_custom_class(self, class_name: str):
        """添加自定义类别"""
        if class_name not in self.class_names:
            class_id = len(self.class_names)
            self.class_names.append(class_name)
            self.class_mapping[class_name] = class_id
    
    def validate_dataset(self, dataset_dir: str) -> Dict:
        """验证YOLO数据集"""
        validation_result = {
            'valid': True,
            'errors': [],
            'warnings': [],
            'statistics': {}
        }
        
        try:
            # 检查目录结构
            required_dirs = [
                'images/train', 'images/val',
                'labels/train', 'labels/val'
            ]
            
            for dir_path in required_dirs:
                full_path = os.path.join(dataset_dir, dir_path)
                if not os.path.exists(full_path):
                    validation_result['errors'].append(f"缺少目录: {dir_path}")
                    validation_result['valid'] = False
            
            # 统计文件数量
            for split in ['train', 'val']:
                images_dir = os.path.join(dataset_dir, 'images', split)
                labels_dir = os.path.join(dataset_dir, 'labels', split)
                
                if os.path.exists(images_dir) and os.path.exists(labels_dir):
                    image_files = [f for f in os.listdir(images_dir) if f.lower().endswith(('.jpg', '.jpeg', '.png'))]
                    label_files = [f for f in os.listdir(labels_dir) if f.endswith('.txt')]
                    
                    validation_result['statistics'][f'{split}_images'] = len(image_files)
                    validation_result['statistics'][f'{split}_labels'] = len(label_files)
                    
                    # 检查图像和标签文件是否匹配
                    image_bases = {os.path.splitext(f)[0] for f in image_files}
                    label_bases = {os.path.splitext(f)[0] for f in label_files}
                    
                    missing_labels = image_bases - label_bases
                    missing_images = label_bases - image_bases
                    
                    if missing_labels:
                        validation_result['warnings'].append(f"{split}集中{len(missing_labels)}个图像缺少标签文件")
                    
                    if missing_images:
                        validation_result['warnings'].append(f"{split}集中{len(missing_images)}个标签文件缺少对应图像")
            
            print("📊 数据集验证结果:")
            print(f"   有效性: {'✅ 有效' if validation_result['valid'] else '❌ 无效'}")
            print(f"   统计信息: {validation_result['statistics']}")
            
            if validation_result['errors']:
                print("   错误:")
                for error in validation_result['errors']:
                    print(f"     - {error}")
            
            if validation_result['warnings']:
                print("   警告:")
                for warning in validation_result['warnings']:
                    print(f"     - {warning}")
            
        except Exception as e:
            validation_result['valid'] = False
            validation_result['errors'].append(f"验证过程出错: {e}")
        
        return validation_result

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="YOLO格式转换工具")
    parser.add_argument('--source', required=True, help="源目录（包含分割结果）")
    parser.add_argument('--output', required=True, help="YOLO数据集输出目录")
    parser.add_argument('--train-ratio', type=float, default=0.8, help="训练集比例")
    parser.add_argument('--validate', action='store_true', help="验证生成的数据集")
    
    args = parser.parse_args()
    
    converter = YOLOFormatConverter()
    
    # 创建YOLO数据集
    dataset_info = converter.create_yolo_dataset(args.source, args.output, args.train_ratio)
    
    if dataset_info and args.validate:
        # 验证数据集
        converter.validate_dataset(args.output)

if __name__ == "__main__":
    main()
