#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GPU-accelerated SAM Everything GUI
- GPU/CPU device selection
- YOLO label generation
- Enhanced performance monitoring
- Official SAM demo parameters
"""

import os
import sys
import cv2
import numpy as np
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
from PIL import Image, ImageTk
import threading
import glob
from sam_everything_gpu import GPUAcceleratedSAMEverything

class GPUAcceleratedSAMEverythingGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("GPU-accelerated SAM Everything - 对象提取工具")
        self.root.geometry("1700x1000")
        
        # 变量
        self.current_image = None
        self.current_image_path = None
        self.current_directory = None
        self.sam_everything = None
        self.result = None
        self.image_files = []
        
        # 创建界面
        self.create_widgets()
        
        # 检查GPU可用性
        self.check_gpu_availability()
        
        # 初始化SAM Everything
        self.initialize_sam_everything()
    
    def check_gpu_availability(self):
        """检查GPU可用性"""
        try:
            import torch
            self.gpu_available = torch.cuda.is_available()
            if self.gpu_available:
                gpu_name = torch.cuda.get_device_name()
                self.log_message(f"检测到GPU: {gpu_name}")
            else:
                self.log_message("未检测到可用GPU，将使用CPU")
        except ImportError:
            self.gpu_available = False
            self.log_message("PyTorch未安装，将使用CPU")
    
    def safe_imread_backup(self, image_path):
        """备用的安全图像读取方法"""
        try:
            # 方法1: 使用numpy和cv2.imdecode
            with open(image_path, 'rb') as f:
                image_data = f.read()
            
            nparr = np.frombuffer(image_data, np.uint8)
            image = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
            
            if image is not None:
                return image
            
            # 方法2: 使用PIL然后转换
            from PIL import Image as PILImage
            pil_image = PILImage.open(image_path)
            if pil_image.mode != 'RGB':
                pil_image = pil_image.convert('RGB')
            
            image_array = np.array(pil_image)
            image_bgr = cv2.cvtColor(image_array, cv2.COLOR_RGB2BGR)
            
            return image_bgr
            
        except Exception as e:
            self.log_message(f"读取图像失败 {image_path}: {e}")
            return None
    
    def create_widgets(self):
        """创建界面组件"""
        # 主框架
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 左侧面板
        left_panel = ttk.Frame(main_frame, width=450)
        left_panel.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 10))
        left_panel.pack_propagate(False)
        
        # 设备选择区域
        self.create_device_selection(left_panel)
        
        # 文件管理区域
        self.create_file_management(left_panel)
        
        # 参数控制区域
        self.create_parameter_controls(left_panel)
        
        # YOLO设置区域
        self.create_yolo_settings(left_panel)
        
        # 操作按钮区域
        self.create_action_buttons(left_panel)
        
        # 状态信息区域
        self.create_status_area(left_panel)
        
        # 右侧图像显示区域
        self.create_image_display(main_frame)
    
    def create_device_selection(self, parent):
        """创建设备选择区域"""
        device_frame = ttk.LabelFrame(parent, text="设备选择")
        device_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # 设备选择
        self.device_var = tk.StringVar(value="auto")
        
        device_options = [
            ("自动选择", "auto"),
            ("强制使用GPU", "gpu"),
            ("强制使用CPU", "cpu")
        ]
        
        for text, value in device_options:
            ttk.Radiobutton(device_frame, text=text, variable=self.device_var, 
                           value=value, command=self.on_device_change).pack(anchor=tk.W, padx=5)
        
        # GPU状态显示
        self.gpu_status_label = ttk.Label(device_frame, text="检查GPU状态中...", 
                                         foreground="blue")
        self.gpu_status_label.pack(anchor=tk.W, padx=5, pady=2)
        
        # 性能监控
        perf_frame = ttk.Frame(device_frame)
        perf_frame.pack(fill=tk.X, padx=5, pady=2)
        
        ttk.Label(perf_frame, text="处理时间:").pack(side=tk.LEFT)
        self.processing_time_label = ttk.Label(perf_frame, text="--", foreground="green")
        self.processing_time_label.pack(side=tk.LEFT, padx=(5, 0))
    
    def create_file_management(self, parent):
        """创建文件管理区域"""
        file_frame = ttk.LabelFrame(parent, text="文件管理")
        file_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 文件夹选择
        folder_frame = ttk.Frame(file_frame)
        folder_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Button(folder_frame, text="选择文件夹", 
                  command=self.select_folder).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(folder_frame, text="选择单个文件", 
                  command=self.select_single_file).pack(side=tk.LEFT)
        
        # 当前文件夹显示
        self.folder_label = ttk.Label(file_frame, text="未选择文件夹", 
                                     wraplength=420, foreground="blue")
        self.folder_label.pack(fill=tk.X, padx=5, pady=2)
        
        # 图像文件列表
        list_frame = ttk.Frame(file_frame)
        list_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        ttk.Label(list_frame, text="图像文件列表:").pack(anchor=tk.W)
        
        # 创建列表框和滚动条
        list_container = ttk.Frame(list_frame)
        list_container.pack(fill=tk.BOTH, expand=True)
        
        self.file_listbox = tk.Listbox(list_container, height=6)
        scrollbar = ttk.Scrollbar(list_container, orient=tk.VERTICAL, 
                                 command=self.file_listbox.yview)
        self.file_listbox.configure(yscrollcommand=scrollbar.set)
        
        self.file_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 绑定列表选择事件
        self.file_listbox.bind('<<ListboxSelect>>', self.on_file_select)
        
        # 文件统计
        self.file_stats_label = ttk.Label(list_frame, text="文件数量: 0")
        self.file_stats_label.pack(anchor=tk.W, pady=2)
    
    def create_parameter_controls(self, parent):
        """创建参数控制区域"""
        param_frame = ttk.LabelFrame(parent, text="SAM参数 (官方Demo默认值)")
        param_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # SAM参数
        ttk.Label(param_frame, text="每边点数:").pack(anchor=tk.W)
        self.points_per_side = tk.IntVar(value=32)
        ttk.Scale(param_frame, from_=16, to=64, variable=self.points_per_side, 
                 orient=tk.HORIZONTAL).pack(fill=tk.X)
        
        ttk.Label(param_frame, text="预测IoU阈值:").pack(anchor=tk.W)
        self.pred_iou_thresh = tk.DoubleVar(value=0.88)
        pred_iou_frame = ttk.Frame(param_frame)
        pred_iou_frame.pack(fill=tk.X)
        tk.Scale(pred_iou_frame, from_=0.5, to=1.0, variable=self.pred_iou_thresh,
                orient=tk.HORIZONTAL, resolution=0.01, length=300).pack(fill=tk.X)

        ttk.Label(param_frame, text="稳定性分数阈值:").pack(anchor=tk.W)
        self.stability_score_thresh = tk.DoubleVar(value=0.95)
        stability_frame = ttk.Frame(param_frame)
        stability_frame.pack(fill=tk.X)
        tk.Scale(stability_frame, from_=0.5, to=1.0, variable=self.stability_score_thresh,
                orient=tk.HORIZONTAL, resolution=0.01, length=300).pack(fill=tk.X)
        
        ttk.Label(param_frame, text="最小对象面积:").pack(anchor=tk.W)
        self.min_object_area = tk.IntVar(value=100)
        ttk.Scale(param_frame, from_=50, to=1000, variable=self.min_object_area, 
                 orient=tk.HORIZONTAL).pack(fill=tk.X)
        
        # 可视化选项
        vis_frame = ttk.LabelFrame(param_frame, text="可视化选项")
        vis_frame.pack(fill=tk.X, pady=5)
        
        self.show_masks = tk.BooleanVar(value=True)
        ttk.Checkbutton(vis_frame, text="显示掩膜", variable=self.show_masks).pack(anchor=tk.W)
        
        ttk.Label(vis_frame, text="掩膜透明度:").pack(anchor=tk.W)
        self.mask_alpha = tk.DoubleVar(value=0.35)
        mask_alpha_frame = ttk.Frame(vis_frame)
        mask_alpha_frame.pack(fill=tk.X)
        tk.Scale(mask_alpha_frame, from_=0.1, to=0.8, variable=self.mask_alpha,
                orient=tk.HORIZONTAL, resolution=0.01, length=300).pack(fill=tk.X)
    
    def create_yolo_settings(self, parent):
        """创建YOLO设置区域"""
        yolo_frame = ttk.LabelFrame(parent, text="YOLO标签设置")
        yolo_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # YOLO标签生成选项
        self.generate_yolo_labels = tk.BooleanVar(value=True)
        ttk.Checkbutton(yolo_frame, text="生成YOLO标签文件", 
                       variable=self.generate_yolo_labels).pack(anchor=tk.W)
        
        # 默认类别ID
        class_frame = ttk.Frame(yolo_frame)
        class_frame.pack(fill=tk.X, pady=2)
        
        ttk.Label(class_frame, text="默认类别ID:").pack(side=tk.LEFT)
        self.default_class_id = tk.IntVar(value=0)
        ttk.Spinbox(class_frame, from_=0, to=999, textvariable=self.default_class_id, 
                   width=10).pack(side=tk.LEFT, padx=(5, 0))
        
        # YOLO标签文件夹状态
        self.yolo_status_label = ttk.Label(yolo_frame, text="YOLO标签将保存到: yolo_label/",
                                          foreground="green")
        self.yolo_status_label.pack(anchor=tk.W, pady=2)

    def create_action_buttons(self, parent):
        """创建操作按钮区域"""
        button_frame = ttk.LabelFrame(parent, text="操作")
        button_frame.pack(fill=tk.X, padx=5, pady=5)

        # 单个处理
        self.process_button = ttk.Button(button_frame, text="处理当前图像",
                                       command=self.process_current_image,
                                       state=tk.DISABLED)
        self.process_button.pack(fill=tk.X, pady=2)

        # 批量处理
        self.batch_button = ttk.Button(button_frame, text="批量处理所有图像",
                                     command=self.batch_process_images,
                                     state=tk.DISABLED)
        self.batch_button.pack(fill=tk.X, pady=2)

        # 输出设置
        output_frame = ttk.Frame(button_frame)
        output_frame.pack(fill=tk.X, pady=5)

        ttk.Label(output_frame, text="输出文件夹:").pack(anchor=tk.W)
        self.output_dir_var = tk.StringVar(value="output")
        ttk.Entry(output_frame, textvariable=self.output_dir_var).pack(fill=tk.X, pady=2)

        ttk.Button(output_frame, text="选择输出文件夹",
                  command=self.select_output_folder).pack(fill=tk.X, pady=2)

    def create_status_area(self, parent):
        """创建状态信息区域"""
        status_frame = ttk.LabelFrame(parent, text="状态信息")
        status_frame.pack(fill=tk.X, padx=5, pady=5)

        # 处理进度
        self.progress_var = tk.StringVar(value="就绪")
        ttk.Label(status_frame, textvariable=self.progress_var).pack(anchor=tk.W)

        self.progress_bar = ttk.Progressbar(status_frame, mode='indeterminate')
        self.progress_bar.pack(fill=tk.X, pady=2)

        # 结果统计
        self.stats_text = tk.Text(status_frame, height=4, wrap=tk.WORD)
        stats_scrollbar = ttk.Scrollbar(status_frame, orient=tk.VERTICAL,
                                       command=self.stats_text.yview)
        self.stats_text.configure(yscrollcommand=stats_scrollbar.set)

        self.stats_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        stats_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

    def create_image_display(self, parent):
        """创建图像显示区域"""
        image_frame = ttk.LabelFrame(parent, text="图像显示")
        image_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)

        # 创建Canvas用于显示图像（更大尺寸）
        self.canvas = tk.Canvas(image_frame, bg='white', width=1100, height=800)
        self.canvas.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # 图像信息标签
        self.image_info_label = ttk.Label(image_frame, text="未加载图像")
        self.image_info_label.pack(pady=2)

    def log_message(self, message):
        """添加日志消息"""
        self.stats_text.insert(tk.END, f"{message}\n")
        self.stats_text.see(tk.END)
        self.root.update()

    def on_device_change(self):
        """设备选择改变事件"""
        device = self.device_var.get()
        self.log_message(f"设备选择改变为: {device}")

        # 重新初始化SAM Everything
        if self.sam_everything is not None:
            self.initialize_sam_everything()

    def initialize_sam_everything(self):
        """初始化SAM Everything"""
        self.log_message("正在初始化GPU-accelerated SAM Everything...")

        # 检查模型文件
        model_path = "sam_vit_h_4b8939.pth"
        if not os.path.exists(model_path):
            self.log_message(f"错误: SAM模型文件不存在: {model_path}")
            messagebox.showerror("错误", f"SAM模型文件不存在: {model_path}\n请确保模型文件在当前目录中")
            return

        try:
            config = self.get_current_config()
            self.sam_everything = GPUAcceleratedSAMEverything(config)

            if self.sam_everything.sam_generator is None:
                self.log_message("错误: SAM Everything初始化失败")
                messagebox.showerror("错误", "SAM Everything初始化失败")
                return

            self.log_message("GPU-accelerated SAM Everything初始化成功!")

            # 更新GPU状态显示
            device = config.get('device', 'auto')
            if device == 'gpu' or (device == 'auto' and self.gpu_available):
                self.gpu_status_label.config(text="当前使用: GPU", foreground="green")
            else:
                self.gpu_status_label.config(text="当前使用: CPU", foreground="orange")

        except Exception as e:
            self.log_message(f"初始化失败: {e}")
            messagebox.showerror("错误", f"初始化失败: {e}")

    def get_current_config(self):
        """获取当前配置"""
        return {
            'checkpoint_path': 'sam_vit_h_4b8939.pth',
            'device': self.device_var.get(),

            # SAM参数 (官方demo默认值)
            'points_per_side': self.points_per_side.get(),
            'pred_iou_thresh': self.pred_iou_thresh.get(),
            'stability_score_thresh': self.stability_score_thresh.get(),
            'min_object_area': self.min_object_area.get(),

            # 可视化参数
            'show_masks': self.show_masks.get(),
            'mask_alpha': self.mask_alpha.get(),

            # YOLO参数
            'generate_yolo_labels': self.generate_yolo_labels.get(),
            'default_class_id': self.default_class_id.get(),
        }

    def select_folder(self):
        """选择文件夹"""
        folder_path = filedialog.askdirectory(title="选择包含图像的文件夹")

        if folder_path:
            self.current_directory = folder_path
            self.folder_label.config(text=f"当前文件夹: {os.path.basename(folder_path)}")

            # 扫描图像文件
            self.scan_image_files()

            # 启用批量处理按钮
            if self.sam_everything is not None and self.image_files:
                self.batch_button.config(state=tk.NORMAL)

            self.log_message(f"已选择文件夹: {folder_path}")

    def select_single_file(self):
        """选择单个文件"""
        file_path = filedialog.askopenfilename(
            title="选择图像文件",
            filetypes=[
                ("图像文件", "*.jpg *.jpeg *.png *.bmp *.tiff"),
                ("所有文件", "*.*")
            ]
        )

        if file_path:
            # 设置文件夹为文件所在目录
            self.current_directory = os.path.dirname(file_path)
            self.folder_label.config(text=f"当前文件夹: {os.path.basename(self.current_directory)}")

            # 扫描图像文件
            self.scan_image_files()

            # 选中当前文件
            filename = os.path.basename(file_path)
            try:
                index = self.image_files.index(filename)
                self.file_listbox.selection_set(index)
                self.load_selected_image()
            except ValueError:
                pass

            self.log_message(f"已选择文件: {filename}")

    def scan_image_files(self):
        """扫描当前目录中的图像文件"""
        if not self.current_directory:
            return

        # 支持的图像格式
        extensions = ['*.jpg', '*.jpeg', '*.png', '*.bmp', '*.tiff', '*.gif']

        self.image_files = []
        for ext in extensions:
            pattern = os.path.join(self.current_directory, ext)
            self.image_files.extend([os.path.basename(f) for f in glob.glob(pattern)])
            # 也搜索大写扩展名
            pattern_upper = os.path.join(self.current_directory, ext.upper())
            self.image_files.extend([os.path.basename(f) for f in glob.glob(pattern_upper)])

        # 去重并排序
        self.image_files = sorted(list(set(self.image_files)))

        # 更新列表框
        self.file_listbox.delete(0, tk.END)
        for filename in self.image_files:
            self.file_listbox.insert(tk.END, filename)

        # 更新统计
        self.file_stats_label.config(text=f"文件数量: {len(self.image_files)}")

        self.log_message(f"扫描到 {len(self.image_files)} 个图像文件")

    def on_file_select(self, event):
        """文件列表选择事件"""
        selection = self.file_listbox.curselection()
        if selection:
            self.load_selected_image()

    def load_selected_image(self):
        """加载选中的图像"""
        selection = self.file_listbox.curselection()
        if not selection or not self.current_directory:
            return

        filename = self.image_files[selection[0]]
        image_path = os.path.join(self.current_directory, filename)

        try:
            # 使用安全方法加载图像
            if self.sam_everything:
                self.current_image = self.sam_everything.safe_imread(image_path)
            else:
                # 如果SAM Everything未初始化，使用备用方法
                self.current_image = self.safe_imread_backup(image_path)

            if self.current_image is None:
                raise ValueError("无法加载图像")

            self.current_image_path = image_path

            # 转换为RGB用于显示
            image_rgb = cv2.cvtColor(self.current_image, cv2.COLOR_BGR2RGB)

            # 显示图像
            self.display_image_on_canvas(image_rgb)

            # 更新图像信息
            h, w = self.current_image.shape[:2]
            self.image_info_label.config(text=f"{filename} - {w}x{h}")

            # 启用处理按钮
            if self.sam_everything is not None:
                self.process_button.config(state=tk.NORMAL)

            self.log_message(f"已加载图像: {filename}")

        except Exception as e:
            self.log_message(f"加载图像失败: {e}")
            messagebox.showerror("错误", f"加载图像失败: {e}")

    def display_image_on_canvas(self, image_array):
        """在Canvas上显示图像（更大尺寸）"""
        # 获取Canvas尺寸
        self.canvas.update()
        canvas_width = self.canvas.winfo_width()
        canvas_height = self.canvas.winfo_height()

        if canvas_width <= 1 or canvas_height <= 1:
            self.root.after(100, lambda: self.display_image_on_canvas(image_array))
            return

        # 计算缩放比例（保持更大的显示尺寸）
        h, w = image_array.shape[:2]
        scale = min(canvas_width / w, canvas_height / h) * 0.95  # 留一些边距

        new_w = int(w * scale)
        new_h = int(h * scale)

        # 调整图像大小
        resized_image = cv2.resize(image_array, (new_w, new_h))

        # 转换为PIL图像
        pil_image = Image.fromarray(resized_image)
        self.photo = ImageTk.PhotoImage(pil_image)

        # 清除Canvas并显示图像
        self.canvas.delete("all")
        x = (canvas_width - new_w) // 2
        y = (canvas_height - new_h) // 2
        self.canvas.create_image(x, y, anchor=tk.NW, image=self.photo)

    def select_output_folder(self):
        """选择输出文件夹"""
        folder_path = filedialog.askdirectory(title="选择输出文件夹")
        if folder_path:
            self.output_dir_var.set(folder_path)
            self.log_message(f"输出文件夹: {folder_path}")

    def process_current_image(self):
        """处理当前图像"""
        if self.current_image is None or self.sam_everything is None:
            return

        self.progress_var.set("正在处理...")
        self.progress_bar.start()
        self.process_button.config(state=tk.DISABLED)

        # 在新线程中处理
        def process_thread():
            try:
                # 更新配置
                config = self.get_current_config()
                self.sam_everything.config.update(config)

                # 创建输出目录
                base_name = os.path.splitext(os.path.basename(self.current_image_path))[0]
                output_dir = os.path.join(self.output_dir_var.get(), base_name)

                # 记录开始时间
                start_time = time.time()

                # 执行处理
                result = self.sam_everything.process_image(
                    self.current_image, self.current_image_path, output_dir)

                # 记录处理时间
                processing_time = time.time() - start_time

                # 在主线程中更新界面
                self.root.after(0, lambda: self.process_complete(result, processing_time))

            except Exception as e:
                self.root.after(0, lambda: self.process_error(str(e)))

        threading.Thread(target=process_thread, daemon=True).start()

    def process_complete(self, result, processing_time):
        """处理完成"""
        self.progress_bar.stop()
        self.progress_var.set("处理完成")
        self.process_button.config(state=tk.NORMAL)

        # 更新处理时间显示
        self.processing_time_label.config(text=f"{processing_time:.2f}秒")

        if not result['success']:
            self.log_message(f"处理失败: {result.get('error', '未知错误')}")
            return

        # 显示结果
        self.log_message(f"处理完成! 用时: {result['processing_time']:.2f}秒")
        self.log_message(f"检测到 {result['objects_count']} 个对象")
        self.log_message(f"结果保存到: {result['output_dir']}")

        # 显示YOLO标签信息
        if result.get('yolo_label_file'):
            self.log_message(f"YOLO标签文件: {result['yolo_label_file']}")

        # 显示结果图像
        if 'result_image' in result:
            result_rgb = cv2.cvtColor(result['result_image'], cv2.COLOR_BGR2RGB)
            self.display_image_on_canvas(result_rgb)

    def process_error(self, error_msg):
        """处理错误"""
        self.progress_bar.stop()
        self.progress_var.set("处理失败")
        self.process_button.config(state=tk.NORMAL)
        self.processing_time_label.config(text="--")
        self.log_message(f"处理失败: {error_msg}")
        messagebox.showerror("错误", f"处理失败: {error_msg}")

    def batch_process_images(self):
        """批量处理图像"""
        if not self.image_files or self.sam_everything is None:
            return

        # 确认批量处理
        if not messagebox.askyesno("确认", f"将处理 {len(self.image_files)} 个图像文件，是否继续？"):
            return

        self.progress_var.set("批量处理中...")
        self.progress_bar.start()
        self.batch_button.config(state=tk.DISABLED)

        # 在新线程中处理
        def batch_process_thread():
            try:
                # 更新配置
                config = self.get_current_config()
                self.sam_everything.config.update(config)

                # 记录开始时间
                start_time = time.time()

                # 执行批量处理
                result = self.sam_everything.batch_process(
                    self.current_directory, self.output_dir_var.get())

                # 记录处理时间
                processing_time = time.time() - start_time

                # 在主线程中更新界面
                self.root.after(0, lambda: self.batch_complete(result, processing_time))

            except Exception as e:
                self.root.after(0, lambda: self.batch_error(str(e)))

        threading.Thread(target=batch_process_thread, daemon=True).start()

    def batch_complete(self, result, processing_time):
        """批量处理完成"""
        self.progress_bar.stop()
        self.progress_var.set("批量处理完成")
        self.batch_button.config(state=tk.NORMAL)

        # 更新处理时间显示
        self.processing_time_label.config(text=f"{processing_time:.2f}秒")

        if not result['success']:
            self.log_message(f"批量处理失败: {result.get('error', '未知错误')}")
            return

        # 显示结果
        self.log_message(f"批量处理完成!")
        self.log_message(f"处理文件: {result['processed_count']}/{result['total_files']}")
        self.log_message(f"总对象数: {result['total_objects']}")
        self.log_message(f"结果保存到: {self.output_dir_var.get()}")
        self.log_message(f"YOLO标签已生成到各自的yolo_label文件夹")

        messagebox.showinfo("完成",
                           f"批量处理完成!\n"
                           f"处理文件: {result['processed_count']}/{result['total_files']}\n"
                           f"总对象数: {result['total_objects']}\n"
                           f"处理时间: {processing_time:.2f}秒")

    def batch_error(self, error_msg):
        """批量处理错误"""
        self.progress_bar.stop()
        self.progress_var.set("批量处理失败")
        self.batch_button.config(state=tk.NORMAL)
        self.processing_time_label.config(text="--")
        self.log_message(f"批量处理失败: {error_msg}")
        messagebox.showerror("错误", f"批量处理失败: {error_msg}")

def main():
    root = tk.Tk()
    app = GPUAcceleratedSAMEverythingGUI(root)
    root.mainloop()

if __name__ == "__main__":
    import time
    main()
