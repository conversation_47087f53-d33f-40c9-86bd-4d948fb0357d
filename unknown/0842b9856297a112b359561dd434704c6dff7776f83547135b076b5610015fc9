#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Simple GUI test to verify tkinter Scale widgets work correctly
"""

import tkinter as tk
from tkinter import ttk

def test_gui():
    """Test the GUI components"""
    root = tk.Tk()
    root.title("GUI Test")
    root.geometry("400x300")
    
    # Test ttk.Scale vs tk.Scale
    frame = ttk.Frame(root)
    frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
    
    # Test regular Scale (should work)
    ttk.Label(frame, text="Regular tk.Scale with resolution:").pack(anchor=tk.W)
    var1 = tk.DoubleVar(value=0.88)
    tk.Scale(frame, from_=0.5, to=1.0, variable=var1, 
             orient=tk.HORIZONTAL, resolution=0.01, length=300).pack(fill=tk.X)
    
    # Test ttk.Scale (without resolution)
    ttk.Label(frame, text="TTK Scale (no resolution):").pack(anchor=tk.W, pady=(10,0))
    var2 = tk.DoubleVar(value=0.95)
    ttk.Scale(frame, from_=0.5, to=1.0, variable=var2, 
              orient=tk.HORIZONTAL).pack(fill=tk.X)
    
    # Display values
    def update_values():
        label_values.config(text=f"Values: {var1.get():.2f}, {var2.get():.2f}")
        root.after(100, update_values)
    
    label_values = ttk.Label(frame, text="Values: 0.88, 0.95")
    label_values.pack(pady=10)
    
    update_values()
    
    ttk.Button(frame, text="Close", command=root.quit).pack(pady=10)
    
    root.mainloop()

if __name__ == "__main__":
    test_gui()
