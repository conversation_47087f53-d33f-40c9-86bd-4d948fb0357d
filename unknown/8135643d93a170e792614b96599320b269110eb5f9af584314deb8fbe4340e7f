#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速修复中文路径问题
专门处理包含中文字符的路径导致的OpenCV读取问题
"""

import os
import cv2
import numpy as np
import glob
import shutil
from PIL import Image

def safe_imread(image_path):
    """安全读取图像，支持中文路径"""
    try:
        # 方法1: 使用numpy和cv2.imdecode
        with open(image_path, 'rb') as f:
            image_data = f.read()
        
        nparr = np.frombuffer(image_data, np.uint8)
        image = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
        
        if image is not None:
            return image
        
        # 方法2: 使用PIL然后转换
        pil_image = Image.open(image_path)
        if pil_image.mode != 'RGB':
            pil_image = pil_image.convert('RGB')
        
        image_array = np.array(pil_image)
        image_bgr = cv2.cvtColor(image_array, cv2.COLOR_RGB2BGR)
        
        return image_bgr
        
    except Exception as e:
        print(f"读取图像失败 {image_path}: {e}")
        return None

def safe_imwrite(image_path, image):
    """安全保存图像，支持中文路径"""
    try:
        ext = os.path.splitext(image_path)[1].lower()
        if ext in ['.jpg', '.jpeg']:
            encode_param = [int(cv2.IMWRITE_JPEG_QUALITY), 95]
            success, encoded_img = cv2.imencode('.jpg', image, encode_param)
        elif ext == '.png':
            encode_param = [int(cv2.IMWRITE_PNG_COMPRESSION), 3]
            success, encoded_img = cv2.imencode('.png', image, encode_param)
        else:
            success, encoded_img = cv2.imencode('.jpg', image)
        
        if success:
            with open(image_path, 'wb') as f:
                f.write(encoded_img.tobytes())
            return True
        
        return False
        
    except Exception as e:
        print(f"保存图像失败 {image_path}: {e}")
        return False

def fix_chinese_path_issue():
    """修复中文路径问题"""
    print("🔧 中文路径问题修复工具")
    print("=" * 50)
    
    # 让用户输入问题路径
    print("请输入包含中文的图像文件夹路径:")
    print("例如: D:/昆明植物所/zhongzi/input_images")
    
    problematic_path = input("路径: ").strip()
    
    if not problematic_path:
        print("❌ 未输入路径")
        return False
    
    print(f"🎯 目标路径: {problematic_path}")
    
    # 检查路径是否存在
    if not os.path.exists(problematic_path):
        print(f"❌ 路径不存在: {problematic_path}")
        return False
    
    # 扫描图像文件
    print(f"\n📁 扫描图像文件...")
    image_extensions = ['*.jpg', '*.jpeg', '*.png', '*.bmp', '*.tiff']
    image_files = []
    
    for ext in image_extensions:
        pattern = os.path.join(problematic_path, ext)
        image_files.extend(glob.glob(pattern))
        # 也搜索大写扩展名
        pattern_upper = os.path.join(problematic_path, ext.upper())
        image_files.extend(glob.glob(pattern_upper))
    
    print(f"📊 找到图像文件: {len(image_files)} 个")
    
    if not image_files:
        print("❌ 未找到图像文件")
        return False
    
    # 显示前几个文件
    print(f"\n📋 图像文件列表:")
    for i, file_path in enumerate(image_files[:10]):
        filename = os.path.basename(file_path)
        file_size = os.path.getsize(file_path)
        print(f"  {i+1:2d}. {filename} ({file_size} 字节)")
    
    if len(image_files) > 10:
        print(f"     ... 还有 {len(image_files) - 10} 个文件")
    
    # 创建安全路径副本
    print(f"\n💾 创建安全路径副本...")
    safe_path = "input_images_safe"
    
    if os.path.exists(safe_path):
        response = input(f"目录 {safe_path} 已存在，是否覆盖? (y/n): ")
        if response.lower() != 'y':
            print("操作取消")
            return True
        shutil.rmtree(safe_path)
    
    os.makedirs(safe_path)
    
    # 复制文件
    copied_count = 0
    failed_count = 0
    
    for i, source_path in enumerate(image_files):
        try:
            # 读取图像
            image = safe_imread(source_path)
            if image is None:
                print(f"❌ 读取失败: {os.path.basename(source_path)}")
                failed_count += 1
                continue
            
            # 生成安全文件名
            original_name = os.path.basename(source_path)
            safe_name = f"image_{i+1:04d}_{original_name}"
            safe_file_path = os.path.join(safe_path, safe_name)
            
            # 保存图像
            success = safe_imwrite(safe_file_path, image)
            if success:
                copied_count += 1
                if copied_count <= 10:  # 只显示前10个
                    print(f"✅ 复制成功: {original_name} -> {safe_name}")
            else:
                failed_count += 1
                print(f"❌ 保存失败: {original_name}")
                
        except Exception as e:
            failed_count += 1
            print(f"❌ 处理失败: {os.path.basename(source_path)} - {e}")
    
    print(f"\n📈 复制结果:")
    print(f"   成功: {copied_count} 个文件")
    print(f"   失败: {failed_count} 个文件")
    print(f"   安全路径: {os.path.abspath(safe_path)}")
    
    if copied_count > 0:
        print(f"\n🎉 修复完成!")
        print(f"✅ 请使用安全路径进行SAM处理: {os.path.abspath(safe_path)}")
        print(f"✅ 在GUI中选择文件夹时，选择: {os.path.abspath(safe_path)}")
        
        # 创建使用说明
        readme_content = f"""# 中文路径修复说明

## 问题
原始路径包含中文字符，导致OpenCV无法正确读取图像文件。

## 解决方案
已将所有图像文件复制到安全路径（纯英文）：
{os.path.abspath(safe_path)}

## 使用方法
1. 启动SAM Everything GUI:
   python sam_everything_gui_simplified.py

2. 选择文件夹时，选择:
   {os.path.abspath(safe_path)}

3. 正常进行图像处理

## 文件对应关系
原始文件 -> 安全文件名
"""
        
        with open(os.path.join(safe_path, "README.txt"), 'w', encoding='utf-8') as f:
            f.write(readme_content)
        
        return True
    else:
        print(f"\n❌ 修复失败，没有成功复制任何文件")
        return False

def main():
    """主函数"""
    try:
        success = fix_chinese_path_issue()
        
        if success:
            print(f"\n🚀 下一步操作:")
            print(f"1. 启动GUI: python sam_everything_gui_simplified.py")
            print(f"2. 选择安全路径文件夹: input_images_safe")
            print(f"3. 开始处理图像")
        else:
            print(f"\n💡 其他解决方案:")
            print(f"1. 检查原始路径是否正确")
            print(f"2. 确保图像文件没有损坏")
            print(f"3. 尝试将图像文件移动到程序目录")
            print(f"4. 使用纯英文路径")
        
        return success
        
    except Exception as e:
        print(f"❌ 修复过程出错: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    import sys
    success = main()
    sys.exit(0 if success else 1)
