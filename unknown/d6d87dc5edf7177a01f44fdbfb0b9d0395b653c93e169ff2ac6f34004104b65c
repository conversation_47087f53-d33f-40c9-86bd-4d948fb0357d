#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
透明背景种子分割命令行工具
专门处理PNG透明背景图像的种子分离
"""

import os
import sys
import argparse
import logging
import time
from pathlib import Path
from transparent_seed_segmentation import TransparentSeedSegmentation

def setup_logging(verbose: bool = False):
    """设置日志配置"""
    level = logging.DEBUG if verbose else logging.INFO
    logging.basicConfig(
        level=level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler('transparent_segmentation.log', encoding='utf-8')
        ]
    )

def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(
        description='透明背景种子分割工具 - 专门处理PNG透明背景图像',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  # 处理单张图像
  python transparent_seed_cli.py --input seeds.png --output ./results

  # 批量处理文件夹
  python transparent_seed_cli.py --input ./png_images --output ./results --batch

  # 自定义参数
  python transparent_seed_cli.py --input seeds.png --output ./results \\
      --min-area 200 --max-area 50000 --padding 15 --alpha-threshold 100
        """
    )
    
    # 基本参数
    parser.add_argument('--input', '-i', required=True,
                       help='输入PNG图像文件或包含PNG文件的目录')
    parser.add_argument('--output', '-o', default='./transparent_output',
                       help='输出目录 (默认: ./transparent_output)')
    
    # 处理模式
    parser.add_argument('--batch', action='store_true',
                       help='批量处理模式（处理目录中的所有PNG文件）')
    parser.add_argument('--extensions', nargs='+', default=['.png'],
                       help='批量处理时的文件扩展名 (默认: .png)')
    
    # 分割参数
    parser.add_argument('--min-area', type=int, default=100,
                       help='最小种子面积（像素） (默认: 100)')
    parser.add_argument('--max-area', type=int, default=100000,
                       help='最大种子面积（像素） (默认: 100000)')
    parser.add_argument('--padding', type=int, default=10,
                       help='裁剪边距（像素） (默认: 10)')
    parser.add_argument('--alpha-threshold', type=int, default=128,
                       help='Alpha通道阈值 (0-255) (默认: 128)')
    parser.add_argument('--connectivity', type=int, choices=[4, 8], default=8,
                       help='连通性 (4或8) (默认: 8)')
    parser.add_argument('--min-alpha-ratio', type=float, default=0.1,
                       help='最小非透明像素比例 (默认: 0.1)')
    
    # 质量和处理选项
    parser.add_argument('--morphology-kernel', type=int, default=3,
                       help='形态学操作核大小 (默认: 3)')
    parser.add_argument('--no-noise-removal', action='store_true',
                       help='禁用噪声移除')
    parser.add_argument('--no-preserve-quality', action='store_true',
                       help='不保持原始质量')
    
    # 输出选项
    parser.add_argument('--generate-yolo', action='store_true',
                       help='生成YOLO训练格式的JSON文件')
    parser.add_argument('--save-marked-crops', action='store_true',
                       help='保存标记区域的单独裁剪图片')
    parser.add_argument('--create-yolo-dataset',
                       help='创建完整YOLO数据集的输出目录')

    # 其他选项
    parser.add_argument('--verbose', '-v', action='store_true',
                       help='启用详细日志输出')
    parser.add_argument('--dry-run', action='store_true',
                       help='试运行模式（不保存文件）')
    
    return parser.parse_args()

def create_config(args):
    """根据命令行参数创建配置字典"""
    return {
        'min_seed_area': args.min_area,
        'max_seed_area': args.max_area,
        'padding': args.padding,
        'alpha_threshold': args.alpha_threshold,
        'connectivity': args.connectivity,
        'morphology_kernel_size': args.morphology_kernel,
        'min_alpha_ratio': args.min_alpha_ratio,
        'output_format': 'png',
        'preserve_quality': not args.no_preserve_quality,
        'remove_noise': not args.no_noise_removal,
    }

def validate_args(args):
    """验证命令行参数"""
    # 检查输入路径
    if not os.path.exists(args.input):
        print(f"错误: 输入路径不存在: {args.input}")
        return False
    
    # 检查批量处理模式
    if args.batch and not os.path.isdir(args.input):
        print(f"错误: 批量处理模式需要输入目录，但提供的是文件: {args.input}")
        return False
    
    if not args.batch and os.path.isdir(args.input):
        print(f"警告: 输入是目录但未启用批量处理模式，将尝试批量处理")
        args.batch = True
    
    # 检查参数范围
    if not (0 <= args.alpha_threshold <= 255):
        print(f"错误: Alpha阈值必须在0-255范围内: {args.alpha_threshold}")
        return False
    
    if args.min_area >= args.max_area:
        print(f"错误: 最小面积必须小于最大面积: {args.min_area} >= {args.max_area}")
        return False
    
    if not (0.0 <= args.min_alpha_ratio <= 1.0):
        print(f"错误: 最小alpha比例必须在0.0-1.0范围内: {args.min_alpha_ratio}")
        return False
    
    return True

def process_single_image(segmentation, input_path, output_dir, logger):
    """处理单张图像"""
    logger.info(f"处理透明背景图像: {input_path}")
    
    # 创建输出目录
    base_name = Path(input_path).stem
    image_output_dir = os.path.join(output_dir, base_name)
    
    # 处理图像
    result = segmentation.process_transparent_image(input_path, image_output_dir)
    
    if result['success']:
        logger.info(f"处理成功!")
        logger.info(f"  提取种子数: {result['seeds_count']}")
        logger.info(f"  处理时间: {result['processing_time']:.2f}秒")
        logger.info(f"  输出目录: {result['output_dir']}")
        logger.info(f"  非透明像素: {result.get('non_transparent_pixels', 'N/A')}")
        
        # 显示每个种子的信息
        for seed in result['seeds']:
            logger.info(f"    种子 {seed['seed_id']}: {seed['filename']} "
                       f"({seed['size'][0]}x{seed['size'][1]}, "
                       f"{seed['file_size']} bytes)")
        
        return True
    else:
        logger.error(f"处理失败: {result.get('error', '未知错误')}")
        return False

def process_batch(segmentation, input_dir, output_dir, extensions, logger):
    """批量处理图像"""
    logger.info(f"批量处理透明背景图像:")
    logger.info(f"  输入目录: {input_dir}")
    logger.info(f"  输出目录: {output_dir}")
    logger.info(f"  支持格式: {extensions}")
    
    # 批量处理
    result = segmentation.batch_process_transparent_images(input_dir, output_dir, extensions)
    
    if result['success']:
        logger.info(f"批量处理完成!")
        logger.info(f"  处理文件: {result['processed_count']}/{result['total_files']}")
        logger.info(f"  总种子数: {result['total_seeds']}")
        logger.info(f"  处理时间: {result['processing_time']:.2f}秒")
        
        # 显示失败的文件
        if result['failed_files']:
            logger.warning(f"失败文件 ({len(result['failed_files'])}):")
            for failed in result['failed_files']:
                logger.warning(f"  {failed['file']}: {failed['error']}")
        
        return True
    else:
        logger.error(f"批量处理失败: {result.get('error', '未知错误')}")
        return False

def main():
    """主函数"""
    args = parse_arguments()
    
    # 设置日志
    setup_logging(args.verbose)
    logger = logging.getLogger(__name__)
    
    # 验证参数
    if not validate_args(args):
        return 1
    
    # 试运行模式提示
    if args.dry_run:
        logger.info("试运行模式 - 不会保存任何文件")
    
    # 创建配置
    config = create_config(args)
    logger.info("透明背景种子分割系统配置:")
    for key, value in config.items():
        logger.info(f"  {key}: {value}")
    
    # 初始化分割系统
    logger.info("初始化透明背景种子分割系统...")
    segmentation = TransparentSeedSegmentation(config)
    
    # 创建输出目录
    if not args.dry_run:
        os.makedirs(args.output, exist_ok=True)
    
    start_time = time.time()
    success = False
    
    if args.batch:
        # 批量处理
        success = process_batch(segmentation, args.input, args.output, args.extensions, logger)
    else:
        # 单文件处理
        success = process_single_image(segmentation, args.input, args.output, logger)
    
    total_time = time.time() - start_time
    logger.info(f"总处理时间: {total_time:.2f}秒")

    # 创建YOLO数据集（如果请求）
    if success and args.create_yolo_dataset:
        logger.info("创建YOLO训练数据集...")
        try:
            from yolo_format_converter import YOLOFormatConverter
            converter = YOLOFormatConverter()
            dataset_info = converter.create_yolo_dataset(args.output, args.create_yolo_dataset)

            if dataset_info:
                logger.info("YOLO数据集创建成功!")
                logger.info(f"  数据集目录: {dataset_info['dataset_dir']}")
                logger.info(f"  训练图像: {dataset_info['train_images']}")
                logger.info(f"  验证图像: {dataset_info['val_images']}")
                logger.info(f"  配置文件: {dataset_info['config_file']}")
            else:
                logger.error("YOLO数据集创建失败")
        except Exception as e:
            logger.error(f"创建YOLO数据集时出错: {e}")

    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
