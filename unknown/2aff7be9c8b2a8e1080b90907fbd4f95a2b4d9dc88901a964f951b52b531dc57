#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
OpenCV兼容性快速修复脚本
自动修复所有分割系统中的OpenCV兼容性问题
"""

import os
import re
import sys

def create_safe_connected_components_function():
    """创建安全的连通组件函数"""
    return '''
def safe_connected_components_with_stats(image, connectivity=8):
    """
    兼容不同OpenCV版本的connectedComponentsWithStats函数
    
    Args:
        image: 二值图像
        connectivity: 连通性 (4 或 8)
        
    Returns:
        (num_labels, labels, stats, centroids)
    """
    try:
        # 尝试新版本OpenCV (4.5+) 的ltype参数
        return cv2.connectedComponentsWithStats(image, connectivity=connectivity, ltype=cv2.CV_32S)
    except TypeError:
        try:
            # 尝试中等版本的位置参数
            return cv2.connectedComponentsWithStats(image, connectivity, cv2.CV_32S)
        except:
            # 回退到最基本的版本
            return cv2.connectedComponentsWithStats(image, connectivity)
'''

def update_file_with_safe_function(file_path):
    """更新文件，添加安全函数并替换调用"""
    if not os.path.exists(file_path):
        print(f"文件不存在: {file_path}")
        return False
    
    print(f"更新文件: {file_path}")
    
    try:
        # 读取文件内容
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否已经包含安全函数
        if 'safe_connected_components_with_stats' in content:
            print(f"  文件已包含安全函数，跳过: {file_path}")
            return True
        
        # 在import语句后添加安全函数
        import_pattern = r'(import cv2\n)'
        safe_function = create_safe_connected_components_function()
        
        if re.search(import_pattern, content):
            content = re.sub(import_pattern, r'\1' + safe_function + '\n', content)
        else:
            # 如果没找到import cv2，在文件开头添加
            content = safe_function + '\n' + content
        
        # 替换所有的connectedComponentsWithStats调用
        patterns_to_replace = [
            # 匹配现有的try-except块
            r'try:\s*\n\s*#[^\n]*\n\s*num_labels, labels, stats, centroids = cv2\.connectedComponentsWithStats\(\s*([^,]+),\s*connectivity=([^,]+),\s*ltype=cv2\.CV_32S\s*\)\s*\n\s*except TypeError:\s*\n\s*try:\s*\n\s*#[^\n]*\n\s*num_labels, labels, stats, centroids = cv2\.connectedComponentsWithStats\(\s*\1,\s*\2,\s*cv2\.CV_32S\s*\)\s*\n\s*except:\s*\n\s*#[^\n]*\n\s*num_labels, labels, stats, centroids = cv2\.connectedComponentsWithStats\(\s*\1,\s*\2\s*\)',
            
            # 匹配简单的调用
            r'cv2\.connectedComponentsWithStats\(\s*([^,]+),\s*connectivity=([^,]+),\s*(?:dtype|ltype)=cv2\.CV_32S\s*\)',
            r'cv2\.connectedComponentsWithStats\(\s*([^,]+),\s*([^,]+),\s*cv2\.CV_32S\s*\)',
        ]
        
        for pattern in patterns_to_replace:
            content = re.sub(
                pattern,
                r'safe_connected_components_with_stats(\1, \2)',
                content,
                flags=re.MULTILINE | re.DOTALL
            )
        
        # 写回文件
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"  ✅ 成功更新: {file_path}")
        return True
        
    except Exception as e:
        print(f"  ❌ 更新失败: {file_path} - {e}")
        return False

def manual_fix_files():
    """手动修复特定文件"""
    files_to_fix = [
        'transparent_seed_segmentation.py',
        'mask_based_segmentation.py'
    ]
    
    for file_path in files_to_fix:
        if os.path.exists(file_path):
            print(f"\n手动修复: {file_path}")
            manual_fix_specific_file(file_path)

def manual_fix_specific_file(file_path):
    """手动修复特定文件的connectedComponentsWithStats调用"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否已经有安全函数
        if 'def safe_connected_components_with_stats' not in content:
            # 在import cv2后添加安全函数
            safe_function = create_safe_connected_components_function()
            
            # 找到import cv2的位置
            import_pos = content.find('import cv2')
            if import_pos != -1:
                # 找到该行的结尾
                line_end = content.find('\n', import_pos)
                if line_end != -1:
                    content = content[:line_end+1] + safe_function + content[line_end+1:]
        
        # 替换复杂的try-except块
        complex_pattern = r'''try:\s*
\s*#[^\n]*\s*
\s*num_labels, labels, stats, centroids = cv2\.connectedComponentsWithStats\(\s*
\s*([^,]+),\s*connectivity=([^,]+),\s*ltype=cv2\.CV_32S\s*
\s*\)\s*
\s*except TypeError:\s*
\s*try:\s*
\s*#[^\n]*\s*
\s*num_labels, labels, stats, centroids = cv2\.connectedComponentsWithStats\(\s*
\s*\1,\s*\2,\s*cv2\.CV_32S\s*
\s*\)\s*
\s*except:\s*
\s*#[^\n]*\s*
\s*num_labels, labels, stats, centroids = cv2\.connectedComponentsWithStats\(\s*
\s*\1,\s*\2\s*
\s*\)'''
        
        replacement = r'num_labels, labels, stats, centroids = safe_connected_components_with_stats(\1, \2)'
        content = re.sub(complex_pattern, replacement, content, flags=re.MULTILINE | re.VERBOSE)
        
        # 写回文件
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"  ✅ 手动修复完成: {file_path}")
        
    except Exception as e:
        print(f"  ❌ 手动修复失败: {file_path} - {e}")

def test_fix():
    """测试修复是否成功"""
    print("\n🧪 测试修复结果...")
    
    try:
        # 测试透明背景分割
        from transparent_seed_segmentation import TransparentSeedSegmentation
        config = {'min_seed_area': 100, 'max_seed_area': 100000}
        segmentation = TransparentSeedSegmentation(config)
        print("  ✅ 透明背景分割模块导入成功")
        
        # 测试掩膜分割
        from mask_based_segmentation import MaskBasedSegmentation
        mask_segmentation = MaskBasedSegmentation(config)
        print("  ✅ 掩膜分割模块导入成功")
        
        print("  ✅ 所有模块修复成功！")
        return True
        
    except Exception as e:
        print(f"  ❌ 测试失败: {e}")
        return False

def create_test_image_and_test():
    """创建测试图像并测试连通组件功能"""
    print("\n🔬 创建测试图像并验证功能...")
    
    try:
        import cv2
        import numpy as np
        
        # 创建测试图像
        test_image = np.zeros((100, 100), dtype=np.uint8)
        cv2.circle(test_image, (30, 30), 15, 255, -1)
        cv2.circle(test_image, (70, 70), 20, 255, -1)
        
        # 测试透明背景分割的连通组件功能
        from transparent_seed_segmentation import TransparentSeedSegmentation
        config = {
            'min_seed_area': 50,
            'max_seed_area': 10000,
            'alpha_threshold': 128,
        }
        
        segmentation = TransparentSeedSegmentation(config)
        
        # 创建BGRA测试图像
        test_bgra = np.zeros((100, 100, 4), dtype=np.uint8)
        test_bgra[:, :, 3] = test_image  # 使用测试图像作为alpha通道
        test_bgra[test_image > 0] = [100, 150, 200, 255]  # 设置颜色
        
        # 测试alpha掩膜提取
        alpha_mask = segmentation.extract_alpha_mask(test_bgra)
        print("  ✅ Alpha掩膜提取成功")
        
        # 测试连通组件查找
        components, labels = segmentation.find_seed_components(alpha_mask)
        print(f"  ✅ 连通组件分析成功，找到 {len(components)} 个组件")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 功能测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🔧 OpenCV兼容性快速修复工具")
    print("=" * 50)
    
    # 检查OpenCV版本
    try:
        import cv2
        print(f"当前OpenCV版本: {cv2.__version__}")
    except ImportError:
        print("❌ OpenCV未安装")
        return 1
    
    # 手动修复文件
    manual_fix_files()
    
    # 测试修复结果
    if test_fix():
        print("\n✅ 修复成功！")
        
        # 进一步测试功能
        if create_test_image_and_test():
            print("✅ 功能验证通过！")
        else:
            print("⚠️  基本修复成功，但功能测试失败")
    else:
        print("\n❌ 修复失败，请手动检查代码")
        return 1
    
    print("\n🎯 修复完成！现在可以正常使用透明背景分割功能了。")
    return 0

if __name__ == "__main__":
    sys.exit(main())
