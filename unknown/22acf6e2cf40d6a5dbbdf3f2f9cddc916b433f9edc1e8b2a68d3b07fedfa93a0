#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
透明背景种子分割系统测试
- 测试alpha通道分析
- 测试种子对象分离
- 测试透明背景保持
- 测试批量处理功能
"""

import os
import sys
import cv2
import numpy as np
import tempfile
import shutil
import unittest
from pathlib import Path
from PIL import Image

# 添加当前目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from transparent_seed_segmentation import TransparentSeedSegmentation
    TRANSPARENT_AVAILABLE = True
except ImportError:
    print("警告: TransparentSeedSegmentation不可用")
    TRANSPARENT_AVAILABLE = False

class TestTransparentSeedSegmentation(unittest.TestCase):
    """透明背景种子分割测试用例"""
    
    def setUp(self):
        """设置测试环境"""
        if not TRANSPARENT_AVAILABLE:
            self.skipTest("TransparentSeedSegmentation不可用")
        
        # 创建临时目录
        self.test_dir = tempfile.mkdtemp()
        
        # 默认配置
        self.config = {
            'min_seed_area': 100,
            'max_seed_area': 100000,
            'padding': 10,
            'alpha_threshold': 128,
            'connectivity': 8,
            'remove_noise': True,
        }
        
        # 初始化分割系统
        self.segmentation = TransparentSeedSegmentation(self.config)
    
    def tearDown(self):
        """清理测试环境"""
        if hasattr(self, 'test_dir') and os.path.exists(self.test_dir):
            shutil.rmtree(self.test_dir)
    
    def create_test_transparent_image(self, width=400, height=300, num_seeds=3):
        """创建测试用的透明背景图像"""
        # 创建RGBA图像（透明背景）
        image = np.zeros((height, width, 4), dtype=np.uint8)
        
        # 添加种子对象（非透明）
        seed_info = []
        
        # 种子1: 大椭圆
        center1 = (100, 100)
        axes1 = (40, 60)
        # 创建椭圆掩膜
        mask1 = np.zeros((height, width), dtype=np.uint8)
        cv2.ellipse(mask1, center1, axes1, 0, 0, 360, 255, -1)
        
        # 设置种子颜色和alpha
        image[mask1 > 0] = [100, 150, 200, 255]  # 蓝色种子，完全不透明
        seed_info.append({'center': center1, 'size': axes1, 'type': 'ellipse'})
        
        # 种子2: 中等圆形
        if num_seeds > 1:
            center2 = (250, 150)
            radius2 = 30
            mask2 = np.zeros((height, width), dtype=np.uint8)
            cv2.circle(mask2, center2, radius2, 255, -1)
            
            image[mask2 > 0] = [200, 100, 150, 255]  # 紫色种子
            seed_info.append({'center': center2, 'size': radius2, 'type': 'circle'})
        
        # 种子3: 小矩形
        if num_seeds > 2:
            rect_coords = (320, 200, 60, 40)  # x, y, w, h
            x, y, w, h = rect_coords
            image[y:y+h, x:x+w] = [150, 200, 100, 255]  # 绿色种子
            seed_info.append({'center': (x+w//2, y+h//2), 'size': (w, h), 'type': 'rectangle'})
        
        return image, seed_info
    
    def save_transparent_image(self, image_rgba, file_path):
        """保存透明背景图像为PNG"""
        # 使用PIL保存以确保透明度正确
        pil_image = Image.fromarray(image_rgba, 'RGBA')
        pil_image.save(file_path, 'PNG')
    
    def test_initialization(self):
        """测试系统初始化"""
        self.assertIsNotNone(self.segmentation)
        self.assertEqual(self.segmentation.config['min_seed_area'], 100)
        self.assertEqual(self.segmentation.config['alpha_threshold'], 128)
        self.assertTrue(self.segmentation.config['remove_noise'])
    
    def test_alpha_channel_reading(self):
        """测试alpha通道读取"""
        # 创建测试图像
        test_image, _ = self.create_test_transparent_image()
        test_path = os.path.join(self.test_dir, "test_alpha.png")
        self.save_transparent_image(test_image, test_path)
        
        # 读取图像
        loaded_image = self.segmentation.safe_imread_with_alpha(test_path)
        
        self.assertIsNotNone(loaded_image)
        self.assertEqual(len(loaded_image.shape), 3)
        self.assertEqual(loaded_image.shape[2], 4)  # BGRA格式
    
    def test_alpha_mask_extraction(self):
        """测试alpha掩膜提取"""
        test_image, _ = self.create_test_transparent_image()
        
        # 转换为BGRA格式
        image_bgra = cv2.cvtColor(test_image, cv2.COLOR_RGBA2BGRA)
        
        # 提取alpha掩膜
        alpha_mask = self.segmentation.extract_alpha_mask(image_bgra)
        
        self.assertEqual(len(alpha_mask.shape), 2)  # 应该是灰度图
        self.assertEqual(alpha_mask.dtype, np.uint8)
        
        # 检查是否有非透明区域
        non_transparent_pixels = np.sum(alpha_mask > 0)
        self.assertGreater(non_transparent_pixels, 0)
    
    def test_seed_component_detection(self):
        """测试种子组件检测"""
        test_image, seed_info = self.create_test_transparent_image(num_seeds=3)
        image_bgra = cv2.cvtColor(test_image, cv2.COLOR_RGBA2BGRA)
        
        # 提取alpha掩膜
        alpha_mask = self.segmentation.extract_alpha_mask(image_bgra)
        
        # 查找组件
        components, labeled_image = self.segmentation.find_seed_components(alpha_mask)
        
        # 应该找到组件（数量可能因形态学操作而变化）
        self.assertGreater(len(components), 0)
        self.assertLessEqual(len(components), 3)
        
        # 检查组件属性
        for comp in components:
            self.assertIn('label', comp)
            self.assertIn('area', comp)
            self.assertIn('bbox', comp)
            self.assertIn('centroid', comp)
            self.assertIn('alpha_ratio', comp)
            
            # 面积应该在配置范围内
            self.assertGreaterEqual(comp['area'], self.config['min_seed_area'])
            self.assertLessEqual(comp['area'], self.config['max_seed_area'])
    
    def test_seed_cropping(self):
        """测试种子裁剪"""
        test_image, _ = self.create_test_transparent_image(num_seeds=2)
        image_bgra = cv2.cvtColor(test_image, cv2.COLOR_RGBA2BGRA)
        
        alpha_mask = self.segmentation.extract_alpha_mask(image_bgra)
        components, _ = self.segmentation.find_seed_components(alpha_mask)
        
        if components:
            component = components[0]
            cropped = self.segmentation.crop_seed_with_alpha(image_bgra, component)
            
            self.assertIsNotNone(cropped)
            self.assertEqual(len(cropped.shape), 3)
            self.assertEqual(cropped.shape[2], 4)  # BGRA格式
            self.assertGreater(cropped.shape[0], 0)  # 高度 > 0
            self.assertGreater(cropped.shape[1], 0)  # 宽度 > 0
    
    def test_seed_saving(self):
        """测试种子保存"""
        # 创建测试裁剪图像
        test_crop = np.random.randint(0, 255, (50, 60, 4), dtype=np.uint8)
        test_crop[:, :, 3] = 255  # 设置为不透明
        
        # 保存种子
        result = self.segmentation.save_cropped_seed(
            test_crop, 1, self.test_dir, "test_image.png"
        )
        
        self.assertIsNotNone(result)
        self.assertEqual(result['seed_id'], 1)
        self.assertTrue(os.path.exists(result['path']))
        self.assertEqual(result['format'], 'png')
        self.assertTrue(result['has_transparency'])
        
        # 检查文件确实被保存
        saved_image = Image.open(result['path'])
        self.assertEqual(saved_image.mode, 'RGBA')
    
    def test_full_processing_pipeline(self):
        """测试完整处理流程"""
        # 创建测试图像
        test_image, _ = self.create_test_transparent_image(num_seeds=2)
        test_path = os.path.join(self.test_dir, "test_transparent.png")
        self.save_transparent_image(test_image, test_path)
        
        # 处理
        result = self.segmentation.process_transparent_image(test_path, self.test_dir)
        
        # 检查结果
        self.assertTrue(result['success'])
        self.assertGreater(result['seeds_count'], 0)
        self.assertGreater(result['processing_time'], 0)
        self.assertEqual(result['method'], 'transparent_seed_segmentation')
        self.assertGreater(result['non_transparent_pixels'], 0)
        
        # 检查输出文件
        if result['visualization_path']:
            self.assertTrue(os.path.exists(result['visualization_path']))
        
        # 检查种子文件
        for seed in result['seeds']:
            self.assertTrue(os.path.exists(seed['path']))
            self.assertEqual(seed['format'], 'png')
    
    def test_batch_processing(self):
        """测试批量处理"""
        # 创建多个测试图像
        for i in range(3):
            test_image, _ = self.create_test_transparent_image(num_seeds=2)
            test_path = os.path.join(self.test_dir, f"test_batch_{i}.png")
            self.save_transparent_image(test_image, test_path)
        
        # 批量处理
        output_dir = os.path.join(self.test_dir, "batch_output")
        result = self.segmentation.batch_process_transparent_images(
            self.test_dir, output_dir, ['.png']
        )
        
        # 检查结果
        self.assertTrue(result['success'])
        self.assertEqual(result['total_files'], 3)
        self.assertGreater(result['processed_count'], 0)
        self.assertGreater(result['total_seeds'], 0)
    
    def test_error_handling(self):
        """测试错误处理"""
        # 测试不存在的文件
        result = self.segmentation.process_transparent_image(
            "nonexistent.png", self.test_dir
        )
        self.assertFalse(result['success'])
        self.assertIn('error', result)
        
        # 测试完全透明的图像
        transparent_image = np.zeros((100, 100, 4), dtype=np.uint8)
        transparent_path = os.path.join(self.test_dir, "transparent.png")
        self.save_transparent_image(transparent_image, transparent_path)
        
        result = self.segmentation.process_transparent_image(transparent_path, self.test_dir)
        self.assertFalse(result['success'])
        self.assertIn('没有非透明区域', result['error'])

def create_demo_transparent_images():
    """创建演示用的透明背景图像"""
    print("创建透明背景演示图像...")
    
    demo_dir = "demo_transparent"
    if os.path.exists(demo_dir):
        shutil.rmtree(demo_dir)
    os.makedirs(demo_dir)
    
    # 创建不同类型的种子图像
    demo_configs = [
        ("多种子样本.png", 5),
        ("双种子样本.png", 2),
        ("单种子样本.png", 1),
        ("复杂种子样本.png", 4),
    ]
    
    for filename, num_seeds in demo_configs:
        # 创建更大的图像
        image = np.zeros((500, 600, 4), dtype=np.uint8)
        
        # 随机生成种子
        np.random.seed(hash(filename) % 1000)
        
        for i in range(num_seeds):
            # 随机位置和大小
            center_x = np.random.randint(50, 550)
            center_y = np.random.randint(50, 450)
            
            if i % 3 == 0:  # 椭圆
                axes = (np.random.randint(20, 50), np.random.randint(30, 70))
                mask = np.zeros((500, 600), dtype=np.uint8)
                cv2.ellipse(mask, (center_x, center_y), axes, 0, 0, 360, 255, -1)
            elif i % 3 == 1:  # 圆形
                radius = np.random.randint(15, 40)
                mask = np.zeros((500, 600), dtype=np.uint8)
                cv2.circle(mask, (center_x, center_y), radius, 255, -1)
            else:  # 矩形
                w, h = np.random.randint(30, 60), np.random.randint(20, 50)
                mask = np.zeros((500, 600), dtype=np.uint8)
                cv2.rectangle(mask, (center_x-w//2, center_y-h//2), 
                             (center_x+w//2, center_y+h//2), 255, -1)
            
            # 随机颜色
            color = [
                np.random.randint(50, 255),
                np.random.randint(50, 255),
                np.random.randint(50, 255),
                255  # 完全不透明
            ]
            
            image[mask > 0] = color
        
        # 保存图像
        file_path = os.path.join(demo_dir, filename)
        pil_image = Image.fromarray(image, 'RGBA')
        pil_image.save(file_path, 'PNG')
        
        print(f"  创建: {filename} ({num_seeds} 个种子)")
    
    print(f"演示图像已创建在: {demo_dir}")
    return demo_dir

def main():
    """运行测试"""
    if len(sys.argv) > 1 and sys.argv[1] == "create-demo":
        create_demo_transparent_images()
        return
    
    # 运行单元测试
    unittest.main(verbosity=2)

if __name__ == "__main__":
    main()
