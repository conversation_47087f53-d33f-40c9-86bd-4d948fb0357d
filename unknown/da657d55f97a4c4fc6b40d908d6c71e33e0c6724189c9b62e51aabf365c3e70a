#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
YOLO依赖安装脚本
自动安装YOLO训练和识别所需的依赖包
"""

import subprocess
import sys
import os

def check_package(package_name):
    """检查包是否已安装"""
    try:
        __import__(package_name)
        return True
    except ImportError:
        return False

def install_package(package_name, pip_name=None):
    """安装包"""
    if pip_name is None:
        pip_name = package_name
    
    print(f"正在安装 {package_name}...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", pip_name])
        print(f"✅ {package_name} 安装成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {package_name} 安装失败: {e}")
        return False

def main():
    """主安装函数"""
    print("🚀 YOLO依赖安装程序")
    print("=" * 50)
    
    # 需要安装的包
    packages = [
        ("ultralytics", "ultralytics"),
        ("torch", "torch"),
        ("torchvision", "torchvision"),
        ("opencv-python", "opencv-python"),
        ("pillow", "Pillow"),
        ("numpy", "numpy"),
        ("matplotlib", "matplotlib"),
        ("requests", "requests"),
    ]
    
    installed_count = 0
    failed_count = 0
    
    for package_name, pip_name in packages:
        print(f"\n📦 检查 {package_name}...")
        
        if check_package(package_name):
            print(f"✅ {package_name} 已安装")
            installed_count += 1
        else:
            print(f"⬇️ {package_name} 未安装，开始安装...")
            if install_package(package_name, pip_name):
                installed_count += 1
            else:
                failed_count += 1
    
    print(f"\n" + "=" * 50)
    print(f"安装总结:")
    print(f"✅ 成功: {installed_count}")
    print(f"❌ 失败: {failed_count}")
    
    if failed_count == 0:
        print(f"\n🎉 所有依赖安装完成！现在可以使用YOLO功能了。")
        
        # 测试YOLO功能
        print(f"\n🧪 测试YOLO功能...")
        try:
            from ultralytics import YOLO
            print("✅ ultralytics 导入成功")
            
            # 测试模型下载
            print("📥 测试模型下载...")
            model = YOLO('yolov8n.pt')  # 这会自动下载模型
            print("✅ 模型下载测试成功")
            
            print(f"\n🎯 YOLO功能已就绪！")
            print(f"现在可以运行: python enhanced_segmentation_gui.py")
            
        except Exception as e:
            print(f"⚠️ YOLO功能测试失败: {e}")
            print(f"请手动检查安装是否正确")
    else:
        print(f"\n⚠️ 部分依赖安装失败，YOLO功能可能不可用")
        print(f"请手动安装失败的包或检查网络连接")
    
    return failed_count == 0

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
