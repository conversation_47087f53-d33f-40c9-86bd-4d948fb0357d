#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分类训练和识别GUI组件
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import os
import threading
from typing import Optional, Dict
import time

from classification_manager import ClassificationManager
from classification_trainer import ClassificationTrainer
from classification_recognizer import ClassificationRecognizer

class ClassificationModelSelector(ttk.Frame):
    """分类模型选择器"""
    
    def __init__(self, parent, manager: ClassificationManager, **kwargs):
        super().__init__(parent, **kwargs)
        self.manager = manager
        self.selected_model = tk.StringVar()
        
        self.create_widgets()
        self.refresh_models()
    
    def create_widgets(self):
        """创建界面"""
        # 模型选择
        model_frame = ttk.LabelFrame(self, text="选择分类模型")
        model_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # 模型下拉框
        self.model_combo = ttk.Combobox(model_frame, textvariable=self.selected_model, 
                                       state="readonly", width=30)
        self.model_combo.pack(side=tk.LEFT, padx=5, pady=5)
        
        # 下载按钮
        self.download_btn = ttk.Button(model_frame, text="下载预训练模型", 
                                      command=self.download_model)
        self.download_btn.pack(side=tk.LEFT, padx=5, pady=5)
        
        # 刷新按钮
        ttk.Button(model_frame, text="刷新", command=self.refresh_models).pack(side=tk.LEFT, padx=5, pady=5)
        
        # 模型信息
        self.info_text = tk.Text(model_frame, height=4, width=50)
        self.info_text.pack(side=tk.RIGHT, padx=5, pady=5)
        
        # 绑定选择事件
        self.model_combo.bind('<<ComboboxSelected>>', self.on_model_selected)
    
    def refresh_models(self):
        """刷新模型列表"""
        models = self.manager.get_available_models()
        model_names = list(models.keys())
        
        self.model_combo['values'] = model_names
        self.models_info = models
        
        if model_names and not self.selected_model.get():
            self.selected_model.set(model_names[0])
            self.on_model_selected()
    
    def on_model_selected(self, event=None):
        """模型选择事件"""
        model_name = self.selected_model.get()
        if model_name and model_name in self.models_info:
            info = self.models_info[model_name]
            
            info_text = f"模型: {info['name']}\n"
            info_text += f"框架: {info['module']}\n"
            info_text += f"状态: {'✅ 可用' if info['available'] else '❌ 不可用'}\n"
            info_text += f"预训练: {'✅ 已下载' if info['downloaded'] else '⬇️ 需下载'}"
            
            self.info_text.delete(1.0, tk.END)
            self.info_text.insert(1.0, info_text)
    
    def download_model(self):
        """下载预训练模型"""
        model_name = self.selected_model.get()
        if not model_name:
            messagebox.showwarning("警告", "请先选择模型")
            return
        
        if not self.models_info[model_name]['available']:
            messagebox.showerror("错误", "该模型不可用，可能缺少依赖库")
            return
        
        if self.models_info[model_name]['downloaded']:
            messagebox.showinfo("信息", "模型已下载")
            return
        
        self.download_btn.config(state="disabled", text="下载中...")
        
        def download_thread():
            try:
                success = self.manager.download_pretrained_model(model_name)
                
                self.after(0, lambda: self.download_complete(success))
                
            except Exception as e:
                self.after(0, lambda: self.download_error(str(e)))
        
        threading.Thread(target=download_thread, daemon=True).start()
    
    def download_complete(self, success: bool):
        """下载完成"""
        self.download_btn.config(state="normal", text="下载预训练模型")
        
        if success:
            messagebox.showinfo("成功", "模型下载完成")
            self.refresh_models()
        else:
            messagebox.showerror("错误", "模型下载失败")
    
    def download_error(self, error_msg: str):
        """下载错误"""
        self.download_btn.config(state="normal", text="下载预训练模型")
        messagebox.showerror("错误", f"下载失败: {error_msg}")
    
    def get_selected_model(self) -> Optional[str]:
        """获取选中的模型"""
        model_name = self.selected_model.get()
        if model_name and model_name in self.models_info:
            if self.models_info[model_name]['available']:
                return model_name
        return None

class ClassificationTrainingPanel(ttk.Frame):
    """分类训练面板"""
    
    def __init__(self, parent, manager: ClassificationManager, trainer: ClassificationTrainer, **kwargs):
        super().__init__(parent, **kwargs)
        self.manager = manager
        self.trainer = trainer
        
        # 训练参数
        self.segmentation_dir = tk.StringVar()
        self.classification_data_dir = tk.StringVar(value="classification_data")
        self.epochs = tk.IntVar(value=50)
        self.learning_rate = tk.DoubleVar(value=0.001)
        self.batch_size = tk.IntVar(value=32)
        self.train_ratio = tk.DoubleVar(value=0.8)
        
        self.create_widgets()
    
    def create_widgets(self):
        """创建界面"""
        # 模型选择器
        self.model_selector = ClassificationModelSelector(self, self.manager)
        self.model_selector.pack(fill=tk.X, padx=5, pady=5)
        
        # 数据设置
        data_frame = ttk.LabelFrame(self, text="数据设置")
        data_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # 分割结果目录
        ttk.Label(data_frame, text="分割结果目录:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=2)
        ttk.Entry(data_frame, textvariable=self.segmentation_dir, width=40).grid(row=0, column=1, padx=5, pady=2)
        ttk.Button(data_frame, text="选择", command=self.select_segmentation_dir).grid(row=0, column=2, padx=5, pady=2)
        
        # 分类数据目录
        ttk.Label(data_frame, text="分类数据目录:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=2)
        ttk.Entry(data_frame, textvariable=self.classification_data_dir, width=40).grid(row=1, column=1, padx=5, pady=2)
        ttk.Button(data_frame, text="选择", command=self.select_classification_dir).grid(row=1, column=2, padx=5, pady=2)
        
        # 准备数据按钮
        ttk.Button(data_frame, text="从分割结果准备分类数据", 
                  command=self.prepare_classification_data).grid(row=2, column=0, columnspan=3, pady=10)
        
        # 训练参数
        params_frame = ttk.LabelFrame(self, text="训练参数")
        params_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # 参数网格
        ttk.Label(params_frame, text="训练轮数:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=2)
        ttk.Spinbox(params_frame, from_=10, to=200, textvariable=self.epochs, width=10).grid(row=0, column=1, padx=5, pady=2)
        
        ttk.Label(params_frame, text="学习率:").grid(row=0, column=2, sticky=tk.W, padx=5, pady=2)
        ttk.Spinbox(params_frame, from_=0.0001, to=0.1, increment=0.0001, 
                   textvariable=self.learning_rate, width=10, format="%.4f").grid(row=0, column=3, padx=5, pady=2)
        
        ttk.Label(params_frame, text="批次大小:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=2)
        ttk.Spinbox(params_frame, from_=8, to=128, textvariable=self.batch_size, width=10).grid(row=1, column=1, padx=5, pady=2)
        
        ttk.Label(params_frame, text="训练集比例:").grid(row=1, column=2, sticky=tk.W, padx=5, pady=2)
        ttk.Spinbox(params_frame, from_=0.5, to=0.95, increment=0.05, 
                   textvariable=self.train_ratio, width=10, format="%.2f").grid(row=1, column=3, padx=5, pady=2)
        
        # 训练控制
        control_frame = ttk.Frame(self)
        control_frame.pack(fill=tk.X, padx=5, pady=5)
        
        self.train_btn = ttk.Button(control_frame, text="开始训练", command=self.start_training)
        self.train_btn.pack(side=tk.LEFT, padx=5)
        
        # 进度条
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(control_frame, variable=self.progress_var, length=200)
        self.progress_bar.pack(side=tk.LEFT, padx=5)
        
        # 训练日志
        log_frame = ttk.LabelFrame(self, text="训练日志")
        log_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        self.log_text = tk.Text(log_frame, wrap=tk.WORD, height=10)
        log_scrollbar = ttk.Scrollbar(log_frame, orient=tk.VERTICAL, command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=log_scrollbar.set)
        self.log_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        log_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    def select_segmentation_dir(self):
        """选择分割结果目录"""
        directory = filedialog.askdirectory(title="选择分割结果目录")
        if directory:
            self.segmentation_dir.set(directory)
    
    def select_classification_dir(self):
        """选择分类数据目录"""
        directory = filedialog.askdirectory(title="选择分类数据目录")
        if directory:
            self.classification_data_dir.set(directory)
    
    def prepare_classification_data(self):
        """准备分类数据"""
        seg_dir = self.segmentation_dir.get()
        class_dir = self.classification_data_dir.get()
        
        if not seg_dir:
            messagebox.showerror("错误", "请选择分割结果目录")
            return
        
        if not class_dir:
            messagebox.showerror("错误", "请设置分类数据目录")
            return
        
        self.log_message("开始准备分类数据...")
        
        def prepare_thread():
            try:
                dataset_info = self.trainer.prepare_training_data_from_segmentation(
                    seg_dir, class_dir
                )
                
                self.after(0, lambda: self.data_prepare_complete(dataset_info))
                
            except Exception as e:
                self.after(0, lambda: self.data_prepare_error(str(e)))
        
        threading.Thread(target=prepare_thread, daemon=True).start()
    
    def data_prepare_complete(self, dataset_info: Dict):
        """数据准备完成"""
        self.log_message("分类数据准备完成！")
        self.log_message(f"类别数: {dataset_info['num_classes']}")
        self.log_message(f"总样本数: {dataset_info['total_samples']}")
        
        for class_name, count in dataset_info['class_counts'].items():
            self.log_message(f"  {class_name}: {count} 个样本")
        
        messagebox.showinfo("成功", "分类数据准备完成！")
    
    def data_prepare_error(self, error_msg: str):
        """数据准备错误"""
        self.log_message(f"数据准备失败: {error_msg}")
        messagebox.showerror("错误", f"数据准备失败: {error_msg}")
    
    def start_training(self):
        """开始训练"""
        model_name = self.model_selector.get_selected_model()
        if not model_name:
            messagebox.showerror("错误", "请选择模型")
            return
        
        class_dir = self.classification_data_dir.get()
        if not class_dir or not os.path.exists(class_dir):
            messagebox.showerror("错误", "分类数据目录不存在")
            return
        
        self.train_btn.config(state="disabled")
        self.progress_var.set(0)
        
        self.log_message("开始训练...")
        self.log_message(f"模型: {model_name}")
        self.log_message(f"数据目录: {class_dir}")
        self.log_message(f"训练参数: epochs={self.epochs.get()}, lr={self.learning_rate.get()}, batch_size={self.batch_size.get()}")
        
        def training_thread():
            try:
                model_path = self.trainer.train_model(
                    model_name=model_name,
                    dataset_dir=class_dir,
                    epochs=self.epochs.get(),
                    learning_rate=self.learning_rate.get(),
                    batch_size=self.batch_size.get(),
                    train_ratio=self.train_ratio.get(),
                    progress_callback=self.update_progress,
                    log_callback=self.log_message
                )
                
                self.after(0, lambda: self.training_complete(model_path))
                
            except Exception as e:
                self.after(0, lambda: self.training_error(str(e)))
        
        threading.Thread(target=training_thread, daemon=True).start()
    
    def training_complete(self, model_path: Optional[str]):
        """训练完成"""
        self.train_btn.config(state="normal")
        
        if model_path:
            self.log_message("训练完成！")
            messagebox.showinfo("成功", f"训练完成！\n模型已保存: {os.path.basename(model_path)}")
        else:
            self.log_message("训练失败")
            messagebox.showerror("错误", "训练失败")
    
    def training_error(self, error_msg: str):
        """训练错误"""
        self.train_btn.config(state="normal")
        self.log_message(f"训练失败: {error_msg}")
        messagebox.showerror("错误", f"训练失败: {error_msg}")
    
    def update_progress(self, progress: float):
        """更新进度"""
        self.progress_var.set(progress)
    
    def log_message(self, message: str):
        """添加日志消息"""
        timestamp = time.strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"
        
        self.log_text.insert(tk.END, log_entry)
        self.log_text.see(tk.END)
        self.update_idletasks()

class ClassificationRecognitionPanel(ttk.Frame):
    """分类识别面板"""

    def __init__(self, parent, recognizer: ClassificationRecognizer, **kwargs):
        super().__init__(parent, **kwargs)
        self.recognizer = recognizer

        # 界面变量
        self.yolo_model_path = tk.StringVar()
        self.classification_model_path = tk.StringVar()
        self.image_path = tk.StringVar()
        self.confidence = tk.DoubleVar(value=0.5)
        self.save_results = tk.BooleanVar(value=True)

        self.create_widgets()

    def create_widgets(self):
        """创建界面"""
        # 模型选择
        models_frame = ttk.LabelFrame(self, text="模型选择")
        models_frame.pack(fill=tk.X, padx=5, pady=5)

        # YOLO模型
        ttk.Label(models_frame, text="YOLO检测模型:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=2)
        ttk.Entry(models_frame, textvariable=self.yolo_model_path, width=40).grid(row=0, column=1, padx=5, pady=2)
        ttk.Button(models_frame, text="选择", command=self.select_yolo_model).grid(row=0, column=2, padx=5, pady=2)

        # 分类模型
        ttk.Label(models_frame, text="分类识别模型:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=2)
        ttk.Entry(models_frame, textvariable=self.classification_model_path, width=40).grid(row=1, column=1, padx=5, pady=2)
        ttk.Button(models_frame, text="选择", command=self.select_classification_model).grid(row=1, column=2, padx=5, pady=2)

        # 加载分类模型按钮
        ttk.Button(models_frame, text="加载分类模型", command=self.load_classification_model).grid(row=2, column=0, columnspan=3, pady=5)

        # 图像选择和参数
        input_frame = ttk.LabelFrame(self, text="输入设置")
        input_frame.pack(fill=tk.X, padx=5, pady=5)

        # 图像文件
        ttk.Label(input_frame, text="图像文件:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=2)
        ttk.Entry(input_frame, textvariable=self.image_path, width=40).grid(row=0, column=1, padx=5, pady=2)
        ttk.Button(input_frame, text="选择", command=self.select_image).grid(row=0, column=2, padx=5, pady=2)

        # 置信度
        ttk.Label(input_frame, text="检测置信度:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=2)
        ttk.Scale(input_frame, from_=0.1, to=1.0, variable=self.confidence,
                 orient=tk.HORIZONTAL, length=200).grid(row=1, column=1, padx=5, pady=2)
        ttk.Label(input_frame, textvariable=self.confidence).grid(row=1, column=2, padx=5, pady=2)

        # 保存结果
        ttk.Checkbutton(input_frame, text="保存识别结果",
                       variable=self.save_results).grid(row=2, column=0, columnspan=3, pady=5)

        # 识别按钮
        self.recognize_btn = ttk.Button(self, text="开始识别", command=self.start_recognition)
        self.recognize_btn.pack(pady=10)

        # 结果显示
        results_frame = ttk.LabelFrame(self, text="识别结果")
        results_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # 创建Notebook用于切换显示
        self.results_notebook = ttk.Notebook(results_frame)
        self.results_notebook.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # 图像显示页面
        image_frame = ttk.Frame(self.results_notebook)
        self.results_notebook.add(image_frame, text="识别图像")

        # 图像画布
        self.results_canvas = tk.Canvas(image_frame, bg='white', width=400, height=300)
        self.results_canvas.pack(fill=tk.BOTH, expand=True)

        # 结果统计页面
        stats_frame = ttk.Frame(self.results_notebook)
        self.results_notebook.add(stats_frame, text="统计结果")

        # 统计文本
        self.stats_text = tk.Text(stats_frame, wrap=tk.WORD)
        stats_scrollbar = ttk.Scrollbar(stats_frame, orient=tk.VERTICAL, command=self.stats_text.yview)
        self.stats_text.configure(yscrollcommand=stats_scrollbar.set)
        self.stats_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        stats_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # 保存图像引用
        self.recognition_photo = None

    def select_yolo_model(self):
        """选择YOLO模型"""
        file_path = filedialog.askopenfilename(
            title="选择YOLO模型文件",
            filetypes=[("PyTorch模型", "*.pt"), ("所有文件", "*.*")]
        )
        if file_path:
            self.yolo_model_path.set(file_path)

    def select_classification_model(self):
        """选择分类模型"""
        file_path = filedialog.askopenfilename(
            title="选择分类模型文件",
            filetypes=[("PyTorch模型", "*.pth"), ("所有文件", "*.*")]
        )
        if file_path:
            self.classification_model_path.set(file_path)

    def select_image(self):
        """选择图像文件"""
        file_path = filedialog.askopenfilename(
            title="选择图像文件",
            filetypes=[("图像文件", "*.jpg *.jpeg *.png *.bmp"), ("所有文件", "*.*")]
        )
        if file_path:
            self.image_path.set(file_path)

    def load_classification_model(self):
        """加载分类模型"""
        model_path = self.classification_model_path.get()
        if not model_path:
            messagebox.showerror("错误", "请先选择分类模型")
            return

        if not os.path.exists(model_path):
            messagebox.showerror("错误", "分类模型文件不存在")
            return

        try:
            success = self.recognizer.load_classification_model(model_path)
            if success:
                model_info = self.recognizer.get_model_info()
                messagebox.showinfo("成功",
                    f"分类模型加载成功！\n"
                    f"模型: {model_info['model_name']}\n"
                    f"类别数: {model_info['num_classes']}\n"
                    f"类别: {', '.join(model_info['classes'])}")
            else:
                messagebox.showerror("错误", "分类模型加载失败")
        except Exception as e:
            messagebox.showerror("错误", f"加载分类模型失败: {e}")

    def start_recognition(self):
        """开始识别"""
        yolo_model = self.yolo_model_path.get()
        image_file = self.image_path.get()

        if not yolo_model:
            messagebox.showerror("错误", "请选择YOLO模型")
            return

        if not image_file:
            messagebox.showerror("错误", "请选择图像文件")
            return

        if self.recognizer.get_model_info() is None:
            messagebox.showerror("错误", "请先加载分类模型")
            return

        self.recognize_btn.config(state="disabled")
        self._show_message("正在识别...")

        def recognition_thread():
            try:
                save_dir = "classification_recognition_results" if self.save_results.get() else None

                results = self.recognizer.predict_image(
                    yolo_model_path=yolo_model,
                    image_path=image_file,
                    confidence=self.confidence.get(),
                    save_dir=save_dir
                )

                self.after(0, lambda: self.recognition_complete(results))

            except Exception as e:
                self.after(0, lambda: self.recognition_error(str(e)))

        threading.Thread(target=recognition_thread, daemon=True).start()

    def recognition_complete(self, results: Optional[Dict]):
        """识别完成"""
        self.recognize_btn.config(state="normal")

        if results:
            self.display_results(results)
        else:
            self._show_message("识别失败或无结果")

    def recognition_error(self, error_msg: str):
        """识别错误"""
        self.recognize_btn.config(state="normal")
        self._show_message(f"识别错误: {error_msg}")

    def _show_message(self, message: str):
        """在画布上显示消息"""
        self.results_canvas.delete("all")
        self.recognition_photo = None

        canvas_width = self.results_canvas.winfo_width()
        canvas_height = self.results_canvas.winfo_height()

        if canvas_width > 1 and canvas_height > 1:
            self.results_canvas.create_text(
                canvas_width // 2, canvas_height // 2,
                text=message, font=("Arial", 12), fill="red", anchor=tk.CENTER
            )

    def display_results(self, results: Dict):
        """显示识别结果"""
        # 显示图像
        if results.get('output_path') and os.path.exists(results['output_path']):
            self._display_image(results['output_path'])
        else:
            self._display_image(results['image_path'])

        # 显示统计结果
        self._display_statistics(results)

        # 切换到图像页面
        self.results_notebook.select(0)

    def _display_image(self, image_path: str):
        """显示图像"""
        try:
            from PIL import Image, ImageTk

            # 加载图像
            image = Image.open(image_path)

            # 获取画布尺寸
            self.results_canvas.update_idletasks()
            canvas_width = self.results_canvas.winfo_width()
            canvas_height = self.results_canvas.winfo_height()

            if canvas_width > 1 and canvas_height > 1:
                # 调整图像大小
                img_width, img_height = image.size
                scale = min(canvas_width / img_width, canvas_height / img_height, 1.0)

                if scale < 1.0:
                    new_width = int(img_width * scale)
                    new_height = int(img_height * scale)
                    image = image.resize((new_width, new_height), Image.Resampling.LANCZOS)

                # 转换为PhotoImage
                self.recognition_photo = ImageTk.PhotoImage(image)

                # 清空画布并显示图像
                self.results_canvas.delete("all")
                self.results_canvas.create_image(
                    canvas_width // 2, canvas_height // 2,
                    image=self.recognition_photo, anchor=tk.CENTER
                )

        except Exception as e:
            self._show_message(f"无法显示图像: {e}")

    def _display_statistics(self, results: Dict):
        """显示统计结果"""
        self.stats_text.delete(1.0, tk.END)

        # 基本信息
        stats_text = "=== 分类识别结果 ===\n\n"
        stats_text += f"图像: {os.path.basename(results['image_path'])}\n"
        stats_text += f"YOLO模型: {os.path.basename(results['yolo_model_path'])}\n"

        model_info = results['classification_model_info']
        stats_text += f"分类模型: {model_info['model_name']}\n"
        stats_text += f"检测到种子数量: {results['total_detections']}\n\n"

        # 详细检测结果
        if results['detections']:
            stats_text += "=== 详细检测结果 ===\n\n"

            for i, detection in enumerate(results['detections']):
                yolo_conf = detection['confidence']
                classification = detection['classification']

                stats_text += f"种子 {i+1}:\n"
                stats_text += f"  检测置信度: {yolo_conf:.3f}\n"
                stats_text += f"  分类结果: {classification['predicted_class']}\n"
                stats_text += f"  分类置信度: {classification['confidence']:.3f}\n"

                # 显示所有类别概率
                stats_text += f"  所有类别概率:\n"
                for class_name, prob in classification['all_probabilities'].items():
                    stats_text += f"    {class_name}: {prob:.3f}\n"
                stats_text += "\n"

        # 分类汇总
        if 'classification_summary' in results:
            summary = results['classification_summary']
            stats_text += "=== 分类汇总 ===\n\n"

            for class_name, count in summary['class_counts'].items():
                avg_conf = summary['average_confidences'][class_name]
                stats_text += f"{class_name}: {count} 个 (平均置信度: {avg_conf:.3f})\n"

        self.stats_text.insert(1.0, stats_text)

def main():
    """测试函数"""
    root = tk.Tk()
    root.title("分类训练测试")
    root.geometry("800x600")
    
    try:
        manager = ClassificationManager()
        trainer = ClassificationTrainer(manager)
        
        panel = ClassificationTrainingPanel(root, manager, trainer)
        panel.pack(fill=tk.BOTH, expand=True)
        
        root.mainloop()
        
    except Exception as e:
        print(f"测试失败: {e}")

if __name__ == "__main__":
    main()
