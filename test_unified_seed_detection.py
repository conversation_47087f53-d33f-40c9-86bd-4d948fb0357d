#!/usr/bin/env python3
"""
测试统一种子检测模式
验证所有组件都正确使用统一的种子检测配置
"""

import os
import json
import sys
from pathlib import Path

def test_yolo_annotations_format():
    """测试YOLO标注格式是否统一"""
    print("🔍 测试YOLO标注格式")
    print("=" * 50)
    
    # 查找现有的YOLO标注文件
    annotation_files = []
    for root, dirs, files in os.walk('.'):
        for file in files:
            if file.endswith('_yolo_annotations.json'):
                annotation_files.append(os.path.join(root, file))
    
    if not annotation_files:
        print("⚠️ 未找到YOLO标注文件")
        print("   请先运行图像分割生成标注数据")
        return False
    
    print(f"✅ 找到 {len(annotation_files)} 个标注文件")
    
    total_seeds = 0
    files_with_multiple_seeds = 0
    
    for annotation_file in annotation_files[:5]:  # 检查前5个文件
        try:
            with open(annotation_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            shapes = data.get('shapes', [])
            seed_count = len(shapes)
            total_seeds += seed_count
            
            if seed_count > 1:
                files_with_multiple_seeds += 1
            
            print(f"   {os.path.basename(annotation_file)}: {seed_count} 个种子")
            
            # 检查标签格式
            for i, shape in enumerate(shapes):
                label = shape.get('label', '')
                description = shape.get('description', '')
                
                if label != 'seed':
                    print(f"   ⚠️ 发现非标准标签: {label}")
                else:
                    print(f"     ✅ 种子 {i+1}: 标签='{label}', 描述='{description[:50]}...'")
                    
        except Exception as e:
            print(f"   ❌ 读取文件失败 {annotation_file}: {e}")
    
    print(f"\n📊 统计结果:")
    print(f"   总种子数: {total_seeds}")
    print(f"   多种子文件: {files_with_multiple_seeds}")
    print(f"   平均每文件种子数: {total_seeds/len(annotation_files):.1f}")
    
    return True

def test_yolo_dataset_config():
    """测试YOLO数据集配置"""
    print("\n📋 测试YOLO数据集配置")
    print("=" * 50)
    
    # 查找数据集配置文件
    dataset_files = []
    for root, dirs, files in os.walk('.'):
        for file in files:
            if file == 'dataset.yaml':
                dataset_files.append(os.path.join(root, file))
    
    if not dataset_files:
        print("⚠️ 未找到数据集配置文件")
        return False
    
    print(f"✅ 找到 {len(dataset_files)} 个数据集配置")
    
    for dataset_file in dataset_files:
        print(f"\n检查配置: {dataset_file}")
        try:
            with open(dataset_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            print("   配置内容预览:")
            lines = content.split('\n')
            for line in lines[:15]:
                if line.strip():
                    print(f"     {line}")
            
            # 检查关键配置
            if 'nc: 1' in content:
                print("   ✅ 类别数量: 1 (统一种子检测)")
            else:
                print("   ⚠️ 类别数量不是1")
            
            if "names: ['seed']" in content or 'names: ["seed"]' in content:
                print("   ✅ 类别名称: seed")
            else:
                print("   ⚠️ 类别名称不是seed")
                
        except Exception as e:
            print(f"   ❌ 读取配置失败: {e}")
    
    return True

def test_yolo_txt_labels():
    """测试YOLO txt标签文件"""
    print("\n📝 测试YOLO txt标签文件")
    print("=" * 50)
    
    # 查找YOLO txt标签文件
    label_files = []
    for root, dirs, files in os.walk('.'):
        if 'labels' in root:
            for file in files:
                if file.endswith('.txt'):
                    label_files.append(os.path.join(root, file))
    
    if not label_files:
        print("⚠️ 未找到YOLO txt标签文件")
        print("   请先创建YOLO数据集")
        return False
    
    print(f"✅ 找到 {len(label_files)} 个标签文件")
    
    class_ids_found = set()
    total_labels = 0
    
    for label_file in label_files[:10]:  # 检查前10个文件
        try:
            with open(label_file, 'r') as f:
                lines = f.readlines()
            
            file_labels = len(lines)
            total_labels += file_labels
            
            print(f"   {os.path.basename(label_file)}: {file_labels} 个标签")
            
            for line in lines:
                parts = line.strip().split()
                if parts:
                    class_id = int(parts[0])
                    class_ids_found.add(class_id)
                    
                    # 验证YOLO格式
                    if len(parts) == 5:
                        center_x, center_y, width, height = map(float, parts[1:])
                        if 0 <= center_x <= 1 and 0 <= center_y <= 1 and 0 <= width <= 1 and 0 <= height <= 1:
                            print(f"     ✅ 类别{class_id}: ({center_x:.3f}, {center_y:.3f}, {width:.3f}, {height:.3f})")
                        else:
                            print(f"     ⚠️ 坐标超出范围: {line.strip()}")
                    else:
                        print(f"     ❌ 格式错误: {line.strip()}")
                        
        except Exception as e:
            print(f"   ❌ 读取标签文件失败 {label_file}: {e}")
    
    print(f"\n📊 标签统计:")
    print(f"   总标签数: {total_labels}")
    print(f"   发现的类别ID: {sorted(class_ids_found)}")
    
    if class_ids_found == {0}:
        print("   ✅ 所有标签都使用类别ID 0 (统一种子检测)")
    else:
        print("   ⚠️ 发现多个类别ID，可能不是统一种子检测模式")
    
    return True

def test_seed_detection_optimizer():
    """测试种子检测优化器"""
    print("\n🌱 测试种子检测优化器")
    print("=" * 50)
    
    try:
        from seed_detection_optimizer import SeedDetectionOptimizer
        optimizer = SeedDetectionOptimizer()
        
        print(f"✅ 优化器初始化成功")
        print(f"   种子类别名称: '{optimizer.seed_class_name}'")
        print(f"   种子类别ID: {optimizer.class_id}")
        
        if optimizer.seed_class_name == "seed" and optimizer.class_id == 0:
            print("   ✅ 配置正确：统一种子检测模式")
        else:
            print("   ⚠️ 配置可能有问题")
        
        return True
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_yolo_format_converter():
    """测试YOLO格式转换器"""
    print("\n🔄 测试YOLO格式转换器")
    print("=" * 50)
    
    try:
        from yolo_format_converter import YOLOFormatConverter
        converter = YOLOFormatConverter()
        
        print(f"✅ 转换器初始化成功")
        print(f"   类别名称: {converter.class_names}")
        print(f"   类别映射: {converter.class_mapping}")
        
        if converter.class_names == ["seed"] and converter.class_mapping == {"seed": 0}:
            print("   ✅ 配置正确：统一种子检测模式")
        else:
            print("   ⚠️ 配置可能有问题")
        
        return True
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def create_sample_unified_annotation():
    """创建示例统一标注文件"""
    print("\n📝 创建示例统一标注")
    print("=" * 50)
    
    sample_annotation = {
        "version": "1.0",
        "flags": {},
        "shapes": [
            {
                "label": "seed",
                "points": [[100, 100], [200, 100], [200, 200], [100, 200]],
                "group_id": None,
                "shape_type": "polygon",
                "flags": {},
                "description": "Seed 1, Area: 10000 pixels, Label: s1"
            },
            {
                "label": "seed",
                "points": [[250, 150], [350, 150], [350, 250], [250, 250]],
                "group_id": None,
                "shape_type": "polygon",
                "flags": {},
                "description": "Seed 2, Area: 10000 pixels, Label: s2"
            }
        ],
        "imagePath": "sample_image.png",
        "imageData": None,
        "imageHeight": 400,
        "imageWidth": 500,
        "lineColor": [0, 255, 0, 128],
        "fillColor": [255, 0, 0, 128],
        "description": "Transparent seed segmentation results for sample_image.png"
    }
    
    sample_file = "sample_unified_yolo_annotations.json"
    try:
        with open(sample_file, 'w', encoding='utf-8') as f:
            json.dump(sample_annotation, f, indent=2, ensure_ascii=False)
        
        print(f"✅ 创建示例标注文件: {sample_file}")
        print(f"   包含 {len(sample_annotation['shapes'])} 个种子")
        print(f"   所有种子都使用 'seed' 标签")
        print(f"   描述中包含 s1、s2 标识")
        
        return True
        
    except Exception as e:
        print(f"❌ 创建示例文件失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🌱 统一种子检测模式测试")
    print("=" * 60)
    
    tests = [
        ("YOLO标注格式", test_yolo_annotations_format),
        ("YOLO数据集配置", test_yolo_dataset_config),
        ("YOLO txt标签", test_yolo_txt_labels),
        ("种子检测优化器", test_seed_detection_optimizer),
        ("YOLO格式转换器", test_yolo_format_converter),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ 测试 {test_name} 时发生错误: {e}")
            results.append((test_name, False))
    
    # 创建示例文件
    create_sample_unified_annotation()
    
    # 总结
    print("\n📊 测试总结")
    print("=" * 60)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
    
    print(f"\n总体结果: {passed}/{total} 项测试通过")
    
    if passed >= total - 1:  # 允许1个测试失败（可能是数据不存在）
        print("🎉 统一种子检测模式配置正确！")
        print("\n📋 配置说明:")
        print("✅ 所有种子统一使用 'seed' 类别")
        print("✅ YOLO训练时类别ID固定为 0")
        print("✅ 多个种子在描述中标记为 s1、s2、s3...")
        print("✅ 训练出的模型专门用于种子检测")
        print("\n🚀 现在可以开始种子检测专用训练了！")
    else:
        print("⚠️ 部分配置可能需要调整")
    
    return passed >= total - 1

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
