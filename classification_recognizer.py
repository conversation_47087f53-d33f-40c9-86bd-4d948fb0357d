#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分类识别模块
结合YOLO目标检测和分类模型进行种子识别
"""

import os
import json
import torch
import torch.nn.functional as F
from pathlib import Path
from typing import Dict, List, Optional, Tuple
import logging
from PIL import Image
import numpy as np
import cv2

from classification_manager import ClassificationManager
from yolo_manager import YOLOManager

class ClassificationRecognizer:
    """分类识别器 - 结合YOLO检测和分类识别"""
    
    def __init__(self, yolo_manager: YOLOManager, classification_manager: ClassificationManager):
        self.yolo_manager = yolo_manager
        self.classification_manager = classification_manager
        self.logger = logging.getLogger(__name__)
        
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        # 当前加载的分类模型
        self.current_classification_model = None
        self.current_model_info = None
        self.current_classes = None
    
    def load_classification_model(self, model_path: str) -> bool:
        """加载分类模型"""
        try:
            self.logger.info(f"加载分类模型: {model_path}")
            
            # 加载模型检查点
            checkpoint = torch.load(model_path, map_location=self.device)
            
            model_name = checkpoint['model_name']
            num_classes = checkpoint['num_classes']
            self.current_classes = checkpoint['classes']
            
            # 创建模型
            model = self.classification_manager.create_model(
                model_name, num_classes, pretrained=False
            )
            
            # 加载权重
            model.load_state_dict(checkpoint['model_state_dict'])
            model = model.to(self.device)
            model.eval()
            
            self.current_classification_model = model
            self.current_model_info = {
                'model_name': model_name,
                'num_classes': num_classes,
                'classes': self.current_classes,
                'model_path': model_path
            }
            
            self.logger.info(f"分类模型加载成功")
            self.logger.info(f"  模型: {model_name}")
            self.logger.info(f"  类别数: {num_classes}")
            self.logger.info(f"  类别: {self.current_classes}")
            
            return True
            
        except Exception as e:
            self.logger.error(f"加载分类模型失败: {e}")
            return False
    
    def predict_image(self, yolo_model_path: str, image_path: str, 
                     confidence: float = 0.5, save_dir: Optional[str] = None) -> Optional[Dict]:
        """
        对图像进行检测+分类识别
        
        Args:
            yolo_model_path: YOLO模型路径
            image_path: 图像路径
            confidence: 检测置信度阈值
            save_dir: 结果保存目录
            
        Returns:
            识别结果字典
        """
        if self.current_classification_model is None:
            raise ValueError("请先加载分类模型")
        
        try:
            # 1. 使用YOLO进行目标检测
            self.logger.info("进行YOLO目标检测...")
            yolo_results = self.yolo_manager.predict_image(
                model_path=yolo_model_path,
                image_path=image_path,
                confidence=confidence,
                save_dir=None  # 我们自己处理保存
            )
            
            if not yolo_results or yolo_results['total_detections'] == 0:
                self.logger.warning("未检测到种子目标")
                return {
                    'image_path': image_path,
                    'yolo_model_path': yolo_model_path,
                    'classification_model_info': self.current_model_info,
                    'detections': [],
                    'total_detections': 0,
                    'output_path': None
                }
            
            # 2. 对检测到的每个种子进行分类
            self.logger.info(f"对 {yolo_results['total_detections']} 个检测目标进行分类...")
            
            # 读取原始图像
            image = Image.open(image_path).convert('RGB')
            
            # 处理每个检测结果
            enhanced_detections = []
            for detection in yolo_results['detections']:
                # 提取种子区域
                bbox = detection['bbox']  # [x1, y1, x2, y2]
                x1, y1, x2, y2 = map(int, bbox)
                
                # 裁剪种子区域
                seed_image = image.crop((x1, y1, x2, y2))
                
                # 进行分类预测
                classification_result = self._classify_seed(seed_image)
                
                # 合并检测和分类结果
                enhanced_detection = {
                    **detection,  # 保留YOLO检测结果
                    'classification': classification_result
                }
                enhanced_detections.append(enhanced_detection)
            
            # 3. 生成带分类标注的结果图像
            output_path = None
            if save_dir:
                output_path = self._save_annotated_image(
                    image_path, enhanced_detections, save_dir
                )
            
            # 4. 构建最终结果
            final_results = {
                'image_path': image_path,
                'yolo_model_path': yolo_model_path,
                'classification_model_info': self.current_model_info,
                'detections': enhanced_detections,
                'total_detections': len(enhanced_detections),
                'output_path': output_path,
                'classification_summary': self._generate_classification_summary(enhanced_detections)
            }
            
            self.logger.info(f"识别完成，检测到 {len(enhanced_detections)} 个种子")
            
            return final_results
            
        except Exception as e:
            self.logger.error(f"识别失败: {e}")
            return None
    
    def _classify_seed(self, seed_image: Image.Image) -> Dict:
        """对单个种子进行分类"""
        try:
            # 应用变换
            transform = self.classification_manager.val_transform
            input_tensor = transform(seed_image).unsqueeze(0).to(self.device)
            
            # 进行预测
            with torch.no_grad():
                outputs = self.current_classification_model(input_tensor)
                probabilities = F.softmax(outputs, dim=1)
                confidence, predicted_class = torch.max(probabilities, 1)
                
                predicted_class = predicted_class.item()
                confidence = confidence.item()
                
                # 获取所有类别的概率
                all_probs = probabilities[0].cpu().numpy()
                
                return {
                    'predicted_class': self.current_classes[predicted_class],
                    'predicted_class_id': predicted_class,
                    'confidence': confidence,
                    'all_probabilities': {
                        self.current_classes[i]: float(prob) 
                        for i, prob in enumerate(all_probs)
                    }
                }
                
        except Exception as e:
            self.logger.error(f"分类预测失败: {e}")
            return {
                'predicted_class': 'unknown',
                'predicted_class_id': -1,
                'confidence': 0.0,
                'all_probabilities': {}
            }
    
    def _save_annotated_image(self, image_path: str, detections: List[Dict], save_dir: str) -> Optional[str]:
        """保存带有检测和分类标注的图像"""
        try:
            # 创建保存目录
            predictions_dir = os.path.join(save_dir, "classification_predictions")
            os.makedirs(predictions_dir, exist_ok=True)
            
            # 读取原始图像
            image = cv2.imread(image_path)
            if image is None:
                return None
            
            # 在图像上绘制检测和分类结果
            for i, detection in enumerate(detections):
                bbox = detection['bbox']  # [x1, y1, x2, y2]
                yolo_confidence = detection['confidence']
                classification = detection['classification']
                
                x1, y1, x2, y2 = map(int, bbox)
                
                # 绘制边界框（绿色）
                cv2.rectangle(image, (x1, y1), (x2, y2), (0, 255, 0), 2)
                
                # 准备标签文本
                seed_label = f"seed{i+1}"
                class_name = classification['predicted_class']
                class_conf = classification['confidence']
                
                # 多行标签
                label_lines = [
                    f"{seed_label} ({yolo_confidence:.2f})",
                    f"{class_name} ({class_conf:.2f})"
                ]
                
                # 计算文本大小
                font = cv2.FONT_HERSHEY_SIMPLEX
                font_scale = 0.5
                thickness = 1
                
                # 绘制标签背景和文本
                y_offset = y1 - 10
                for line in label_lines:
                    (text_width, text_height), baseline = cv2.getTextSize(line, font, font_scale, thickness)
                    
                    # 绘制背景
                    cv2.rectangle(image, (x1, y_offset - text_height - 5), 
                                (x1 + text_width, y_offset), (0, 255, 0), -1)
                    
                    # 绘制文本
                    cv2.putText(image, line, (x1, y_offset - 2), font, font_scale, (0, 0, 0), thickness)
                    
                    y_offset -= (text_height + 8)
            
            # 保存标注后的图像
            image_name = os.path.basename(image_path)
            output_path = os.path.join(predictions_dir, image_name)
            
            success = cv2.imwrite(output_path, image)
            if success:
                self.logger.info(f"分类识别结果图像已保存: {output_path}")
                return output_path
            else:
                return None
                
        except Exception as e:
            self.logger.error(f"保存标注图像失败: {e}")
            return None
    
    def _generate_classification_summary(self, detections: List[Dict]) -> Dict:
        """生成分类汇总统计"""
        summary = {
            'total_seeds': len(detections),
            'class_counts': {},
            'average_confidences': {}
        }
        
        # 统计各类别数量和平均置信度
        class_confidences = {}
        
        for detection in detections:
            classification = detection['classification']
            class_name = classification['predicted_class']
            confidence = classification['confidence']
            
            if class_name not in summary['class_counts']:
                summary['class_counts'][class_name] = 0
                class_confidences[class_name] = []
            
            summary['class_counts'][class_name] += 1
            class_confidences[class_name].append(confidence)
        
        # 计算平均置信度
        for class_name, confidences in class_confidences.items():
            summary['average_confidences'][class_name] = sum(confidences) / len(confidences)
        
        return summary
    
    def get_model_info(self) -> Optional[Dict]:
        """获取当前分类模型信息"""
        return self.current_model_info

def main():
    """测试函数"""
    print("分类识别器测试")
    print("=" * 50)
    
    try:
        from yolo_manager import YOLOManager
        from classification_manager import ClassificationManager
        
        yolo_manager = YOLOManager()
        classification_manager = ClassificationManager()
        recognizer = ClassificationRecognizer(yolo_manager, classification_manager)
        
        print("✅ 分类识别器初始化成功")
        
        # 检查可用的分类模型
        trained_models = classification_manager.list_trained_models()
        if trained_models:
            print(f"✅ 找到 {len(trained_models)} 个训练好的分类模型")
            for model in trained_models[:3]:
                print(f"   - {os.path.basename(model)}")
        else:
            print("⚠️ 未找到训练好的分类模型")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")

if __name__ == "__main__":
    main()
