#!/usr/bin/env python3
"""
验证分类系统集成
"""

def check_files():
    """检查文件是否存在"""
    print("检查文件...")
    
    required_files = [
        'classification_manager.py',
        'classification_trainer.py', 
        'classification_recognizer.py',
        'classification_gui_components.py',
        'enhanced_segmentation_gui.py'
    ]
    
    missing_files = []
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
        else:
            print(f"✅ {file}")
    
    if missing_files:
        print(f"❌ 缺少文件: {missing_files}")
        return False
    
    return True

def check_imports():
    """检查导入"""
    print("\n检查导入...")
    
    try:
        from classification_manager import ClassificationManager
        print("✅ ClassificationManager")
    except Exception as e:
        print(f"❌ ClassificationManager: {e}")
        return False
    
    try:
        from classification_trainer import ClassificationTrainer
        print("✅ ClassificationTrainer")
    except Exception as e:
        print(f"❌ ClassificationTrainer: {e}")
        return False
    
    try:
        from classification_recognizer import ClassificationRecognizer
        print("✅ ClassificationRecognizer")
    except Exception as e:
        print(f"❌ ClassificationRecognizer: {e}")
        return False
    
    try:
        from classification_gui_components import ClassificationTrainingPanel, ClassificationRecognitionPanel
        print("✅ GUI组件")
    except Exception as e:
        print(f"❌ GUI组件: {e}")
        return False
    
    return True

def check_main_gui():
    """检查主界面修改"""
    print("\n检查主界面修改...")
    
    try:
        with open('enhanced_segmentation_gui.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        checks = [
            ('目标检测', '标签页名称修改'),
            ('YOLO训练', '标签页名称修改'),
            ('分类训练', '新标签页'),
            ('分类识别', '新标签页'),
            ('create_classification_training_interface', '训练界面方法'),
            ('create_classification_recognition_interface', '识别界面方法'),
            ('classification_manager', '分类管理器'),
        ]
        
        for check_text, description in checks:
            if check_text in content:
                print(f"✅ {description}")
            else:
                print(f"❌ {description}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ 检查主界面失败: {e}")
        return False

def main():
    import os
    
    print("🌱 分类系统集成验证")
    print("=" * 40)
    
    tests = [
        check_files,
        check_imports,
        check_main_gui
    ]
    
    passed = 0
    for test in tests:
        if test():
            passed += 1
    
    print(f"\n结果: {passed}/{len(tests)} 项检查通过")
    
    if passed == len(tests):
        print("🎉 分类系统集成成功！")
        print("\n📋 新增功能:")
        print("✅ 分类训练 - 基于分割结果训练分类模型")
        print("✅ 分类识别 - YOLO检测 + 分类识别")
        print("✅ 多种分类模型 - ResNet、EfficientNet、MobileNet")
        print("✅ 完整工作流程 - 分割→检测→分类")
        print("\n🚀 可以启动GUI测试完整功能了！")
    else:
        print("⚠️ 部分功能存在问题")

if __name__ == "__main__":
    main()
