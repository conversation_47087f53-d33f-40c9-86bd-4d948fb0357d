#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试OpenCV兼容性修复
验证透明背景分割和掩膜分割是否正常工作
"""

import os
import sys
import cv2
import numpy as np
import tempfile
from PIL import Image

def test_opencv_version():
    """测试OpenCV版本"""
    print(f"OpenCV版本: {cv2.__version__}")
    
    # 测试基本的connectedComponentsWithStats
    test_image = np.zeros((50, 50), dtype=np.uint8)
    cv2.circle(test_image, (20, 20), 10, 255, -1)
    
    try:
        result = cv2.connectedComponentsWithStats(test_image, 8)
        print(f"✅ 基本connectedComponentsWithStats工作正常，找到 {result[0]-1} 个组件")
        return True
    except Exception as e:
        print(f"❌ 基本connectedComponentsWithStats失败: {e}")
        return False

def test_transparent_segmentation():
    """测试透明背景分割"""
    print("\n测试透明背景分割模块...")
    
    try:
        from transparent_seed_segmentation import TransparentSeedSegmentation
        
        # 创建配置
        config = {
            'min_seed_area': 50,
            'max_seed_area': 10000,
            'alpha_threshold': 128,
            'padding': 5,
            'remove_noise': True,
        }
        
        # 初始化系统
        segmentation = TransparentSeedSegmentation(config)
        print("✅ 透明背景分割系统初始化成功")
        
        # 创建测试图像
        test_image = create_test_transparent_image()
        
        # 测试alpha掩膜提取
        alpha_mask = segmentation.extract_alpha_mask(test_image)
        print("✅ Alpha掩膜提取成功")
        
        # 测试连通组件分析
        components, labels = segmentation.find_seed_components(alpha_mask)
        print(f"✅ 连通组件分析成功，找到 {len(components)} 个种子组件")
        
        return True
        
    except Exception as e:
        print(f"❌ 透明背景分割测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_mask_segmentation():
    """测试掩膜分割"""
    print("\n测试掩膜分割模块...")
    
    try:
        from mask_based_segmentation import MaskBasedSegmentation
        
        # 创建配置
        config = {
            'min_seed_area': 50,
            'max_seed_area': 10000,
            'padding': 5,
            'invert_mask': True,
        }
        
        # 初始化系统
        segmentation = MaskBasedSegmentation(config)
        print("✅ 掩膜分割系统初始化成功")
        
        # 创建测试掩膜
        test_mask = create_test_mask()
        
        # 测试掩膜预处理
        binary_mask = segmentation.preprocess_mask(test_mask)
        print("✅ 掩膜预处理成功")
        
        # 测试连通组件分析
        components, labels = segmentation.find_connected_components(binary_mask)
        print(f"✅ 连通组件分析成功，找到 {len(components)} 个组件")
        
        return True
        
    except Exception as e:
        print(f"❌ 掩膜分割测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def create_test_transparent_image():
    """创建测试用的透明背景图像"""
    # 创建BGRA图像
    image = np.zeros((100, 100, 4), dtype=np.uint8)
    
    # 添加两个种子（非透明区域）
    cv2.circle(image, (30, 30), 15, [100, 150, 200, 255], -1)
    cv2.circle(image, (70, 70), 12, [200, 100, 150, 255], -1)
    
    return image

def create_test_mask():
    """创建测试用的掩膜图像"""
    # 创建白底黑种子的掩膜
    mask = np.ones((100, 100), dtype=np.uint8) * 255
    
    # 添加黑色种子区域
    cv2.circle(mask, (30, 30), 15, 0, -1)
    cv2.circle(mask, (70, 70), 12, 0, -1)
    
    return mask

def test_full_pipeline():
    """测试完整处理流程"""
    print("\n测试完整处理流程...")
    
    # 创建临时目录
    temp_dir = tempfile.mkdtemp()
    
    try:
        # 测试透明背景分割完整流程
        print("测试透明背景分割完整流程...")
        
        # 创建测试PNG图像
        test_image = create_test_transparent_image()
        test_path = os.path.join(temp_dir, "test_transparent.png")
        
        # 保存为PNG
        pil_image = Image.fromarray(test_image, 'RGBA')
        pil_image.save(test_path, 'PNG')
        
        # 处理图像
        from transparent_seed_segmentation import TransparentSeedSegmentation
        config = {'min_seed_area': 50, 'max_seed_area': 10000}
        segmentation = TransparentSeedSegmentation(config)
        
        result = segmentation.process_transparent_image(test_path, temp_dir)
        
        if result['success']:
            print(f"✅ 透明背景分割完整流程成功，提取 {result['seeds_count']} 个种子")
        else:
            print(f"❌ 透明背景分割完整流程失败: {result.get('error', '未知错误')}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 完整流程测试失败: {e}")
        return False
    
    finally:
        # 清理临时文件
        import shutil
        shutil.rmtree(temp_dir, ignore_errors=True)

def main():
    """主测试函数"""
    print("🧪 OpenCV兼容性修复验证")
    print("=" * 40)
    
    # 测试OpenCV基本功能
    if not test_opencv_version():
        print("❌ OpenCV基本功能测试失败")
        return 1
    
    # 测试各个模块
    tests = [
        ("透明背景分割", test_transparent_segmentation),
        ("掩膜分割", test_mask_segmentation),
        ("完整流程", test_full_pipeline),
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        print(f"\n🔬 {test_name}测试")
        print("-" * 20)
        
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name}测试通过")
            else:
                failed += 1
                print(f"❌ {test_name}测试失败")
        except Exception as e:
            failed += 1
            print(f"❌ {test_name}测试异常: {e}")
    
    # 总结
    print("\n" + "=" * 40)
    print("测试总结:")
    print(f"✅ 通过: {passed}")
    print(f"❌ 失败: {failed}")
    print(f"📊 总计: {passed + failed}")
    
    if failed == 0:
        print("\n🎉 所有测试通过！OpenCV兼容性修复成功！")
        print("\n现在可以正常使用以下功能:")
        print("- python transparent_seed_cli.py")
        print("- python enhanced_segmentation_gui.py")
        print("- 透明背景分割和掩膜分割功能")
        return 0
    else:
        print(f"\n💥 {failed} 个测试失败！")
        print("请检查OpenCV版本或手动修复代码")
        return 1

if __name__ == "__main__":
    sys.exit(main())
