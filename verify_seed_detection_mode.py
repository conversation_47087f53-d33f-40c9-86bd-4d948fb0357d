#!/usr/bin/env python3
"""
验证统一种子检测模式配置
"""

import os
import json

def check_existing_annotations():
    """检查现有标注文件"""
    print("检查现有YOLO标注文件...")
    
    annotation_files = []
    for root, dirs, files in os.walk('.'):
        for file in files:
            if file.endswith('_yolo_annotations.json'):
                annotation_files.append(os.path.join(root, file))
    
    if annotation_files:
        print(f"找到 {len(annotation_files)} 个标注文件")
        
        # 检查第一个文件
        try:
            with open(annotation_files[0], 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            shapes = data.get('shapes', [])
            print(f"示例文件包含 {len(shapes)} 个种子")
            
            for i, shape in enumerate(shapes[:3]):
                label = shape.get('label', '')
                description = shape.get('description', '')
                print(f"  种子 {i+1}: 标签='{label}', 描述='{description}'")
                
        except Exception as e:
            print(f"读取文件失败: {e}")
    else:
        print("未找到标注文件")

def check_components():
    """检查关键组件"""
    print("\n检查关键组件...")
    
    # 检查种子检测优化器
    try:
        from seed_detection_optimizer import SeedDetectionOptimizer
        optimizer = SeedDetectionOptimizer()
        print(f"✅ 种子检测优化器: 类别='{optimizer.seed_class_name}', ID={optimizer.class_id}")
    except Exception as e:
        print(f"❌ 种子检测优化器: {e}")
    
    # 检查YOLO格式转换器
    try:
        from yolo_format_converter import YOLOFormatConverter
        converter = YOLOFormatConverter()
        print(f"✅ YOLO格式转换器: 类别={converter.class_names}, 映射={converter.class_mapping}")
    except Exception as e:
        print(f"❌ YOLO格式转换器: {e}")

def main():
    print("🌱 验证统一种子检测模式")
    print("=" * 40)
    
    check_existing_annotations()
    check_components()
    
    print("\n✅ 配置验证完成")
    print("\n📋 统一种子检测模式说明:")
    print("- 所有种子统一使用 'seed' 类别")
    print("- YOLO训练时类别ID固定为 0")
    print("- 多个种子在描述中标记为 s1、s2、s3...")
    print("- 训练出的模型专门用于种子检测")

if __name__ == "__main__":
    main()
