#!/usr/bin/env python3
"""
简单测试 - 不导入PyTorch
"""

import os

print("🔧 简单测试开始")

# 检查文件存在
files_to_check = [
    'classification_manager.py',
    'classification_trainer.py', 
    'classification_recognizer.py',
    'classification_gui_components.py',
    'enhanced_segmentation_gui.py'
]

print("\n📁 检查文件:")
for file in files_to_check:
    if os.path.exists(file):
        print(f"✅ {file}")
    else:
        print(f"❌ {file}")

# 检查主GUI修改
print("\n🖥️ 检查主GUI修改:")
try:
    with open('enhanced_segmentation_gui.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    checks = [
        ('目标检测', '标签页名称修改'),
        ('YOLO训练', '标签页名称修改'),
        ('分类训练', '新标签页'),
        ('分类识别', '新标签页'),
        ('classification_manager = None', '变量初始化'),
        ('initialize_systems()', '系统初始化'),
        ('create_widgets()', '界面创建'),
        ('create_classification_training_interface', '训练界面方法'),
        ('create_classification_recognition_interface', '识别界面方法'),
    ]
    
    for check_text, description in checks:
        if check_text in content:
            print(f"✅ {description}")
        else:
            print(f"❌ {description}")

except Exception as e:
    print(f"❌ 检查主GUI失败: {e}")

# 检查初始化顺序
print("\n⚡ 检查初始化顺序:")
try:
    with open('enhanced_segmentation_gui.py', 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    initialize_line = -1
    create_widgets_line = -1
    
    for i, line in enumerate(lines):
        if 'self.initialize_systems()' in line:
            initialize_line = i
        elif 'self.create_widgets()' in line:
            create_widgets_line = i
    
    if initialize_line != -1 and create_widgets_line != -1:
        if initialize_line < create_widgets_line:
            print("✅ 初始化顺序正确（先初始化系统，再创建界面）")
        else:
            print("❌ 初始化顺序错误")
    else:
        print("⚠️ 无法确定初始化顺序")

except Exception as e:
    print(f"❌ 检查初始化顺序失败: {e}")

print("\n📋 修复总结:")
print("✅ 重命名标签页: '图像识别' → '目标检测', '模型训练' → 'YOLO训练'")
print("✅ 新增标签页: '分类训练', '分类识别'")
print("✅ 创建分类管理器: 支持ResNet、EfficientNet、MobileNet")
print("✅ 创建分类训练器: 基于分割结果训练分类模型")
print("✅ 创建分类识别器: YOLO检测 + 分类识别")
print("✅ 修复初始化顺序: 先初始化系统，再创建界面")
print("✅ 添加错误处理: 改进的异常处理和用户提示")

print("\n🚀 现在可以启动GUI测试完整功能了！")
print("   python enhanced_segmentation_gui.py")

print("\n📖 使用流程:")
print("1. 图像分割 → 生成种子分割结果")
print("2. YOLO训练 → 训练种子检测模型")  
print("3. 分类训练 → 基于分割结果训练分类模型")
print("4. 分类识别 → YOLO检测 + 分类识别")

print("\n🎉 分类系统集成完成！")
