#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
YOLO模型管理器
处理YOLO模型下载、训练和推理
"""

import os
import sys
import json
import time
import shutil
import requests
import threading
from pathlib import Path
from typing import Dict, List, Optional, Callable
import logging

# 尝试导入ultralytics
try:
    from ultralytics import YOLO
    ULTRALYTICS_AVAILABLE = True
except ImportError:
    ULTRALYTICS_AVAILABLE = False
    print("警告: ultralytics未安装，YOLO功能将不可用")
    print("安装命令: pip install ultralytics")

class YOLOManager:
    """YOLO模型管理器"""
    
    def __init__(self, models_dir: str = "yolo_models"):
        self.models_dir = Path(models_dir)
        self.models_dir.mkdir(exist_ok=True)
        
        # 预设模型配置
        self.preset_models = {
            # YOLOv8 系列
            "yolov8n": {
                "name": "YOLOv8 Nano",
                "description": "最快速度，适合实时检测",
                "url": "https://github.com/ultralytics/assets/releases/download/v8.2.0/yolov8n.pt",
                "size": "6.2MB",
                "speed": "极快",
                "accuracy": "中等"
            },
            "yolov8s": {
                "name": "YOLOv8 Small", 
                "description": "速度与精度平衡",
                "url": "https://github.com/ultralytics/assets/releases/download/v8.2.0/yolov8s.pt",
                "size": "21.5MB",
                "speed": "快",
                "accuracy": "良好"
            },
            "yolov8m": {
                "name": "YOLOv8 Medium",
                "description": "高精度检测",
                "url": "https://github.com/ultralytics/assets/releases/download/v8.2.0/yolov8m.pt",
                "size": "49.7MB", 
                "speed": "中等",
                "accuracy": "高"
            },
            "yolov8l": {
                "name": "YOLOv8 Large",
                "description": "更高精度",
                "url": "https://github.com/ultralytics/assets/releases/download/v8.2.0/yolov8l.pt",
                "size": "83.7MB",
                "speed": "慢",
                "accuracy": "很高"
            },
            "yolov8x": {
                "name": "YOLOv8 Extra Large",
                "description": "最高精度",
                "url": "https://github.com/ultralytics/assets/releases/download/v8.2.0/yolov8x.pt",
                "size": "136MB",
                "speed": "很慢", 
                "accuracy": "最高"
            },
            
            # YOLOv9 系列
            "yolov9c": {
                "name": "YOLOv9 Compact",
                "description": "YOLOv9紧凑版",
                "url": "https://github.com/WongKinYiu/yolov9/releases/download/v0.1/yolov9-c.pt",
                "size": "51.8MB",
                "speed": "中等",
                "accuracy": "高"
            },
            "yolov9e": {
                "name": "YOLOv9 Extended", 
                "description": "YOLOv9扩展版",
                "url": "https://github.com/WongKinYiu/yolov9/releases/download/v0.1/yolov9-e.pt",
                "size": "115MB",
                "speed": "慢",
                "accuracy": "很高"
            },
            
            # YOLOv10 系列
            "yolov10n": {
                "name": "YOLOv10 Nano",
                "description": "YOLOv10最快版本",
                "url": "https://github.com/THU-MIG/yolov10/releases/download/v1.1/yolov10n.pt",
                "size": "5.8MB",
                "speed": "极快",
                "accuracy": "中等"
            },
            "yolov10s": {
                "name": "YOLOv10 Small",
                "description": "YOLOv10小型版本",
                "url": "https://github.com/THU-MIG/yolov10/releases/download/v1.1/yolov10s.pt", 
                "size": "16.5MB",
                "speed": "快",
                "accuracy": "良好"
            },
            "yolov10m": {
                "name": "YOLOv10 Medium",
                "description": "YOLOv10中型版本",
                "url": "https://github.com/THU-MIG/yolov10/releases/download/v1.1/yolov10m.pt",
                "size": "33.4MB",
                "speed": "中等", 
                "accuracy": "高"
            },
            
            # YOLOv11 系列
            "yolov11n": {
                "name": "YOLOv11 Nano",
                "description": "YOLOv11最新纳米版",
                "url": "https://github.com/ultralytics/assets/releases/download/v8.3.0/yolo11n.pt",
                "size": "5.1MB",
                "speed": "极快",
                "accuracy": "中等"
            },
            "yolov11s": {
                "name": "YOLOv11 Small", 
                "description": "YOLOv11最新小型版",
                "url": "https://github.com/ultralytics/assets/releases/download/v8.3.0/yolo11s.pt",
                "size": "19.8MB",
                "speed": "快",
                "accuracy": "良好"
            },
            "yolov11m": {
                "name": "YOLOv11 Medium",
                "description": "YOLOv11最新中型版",
                "url": "https://github.com/ultralytics/assets/releases/download/v8.3.0/yolo11m.pt",
                "size": "48.8MB",
                "speed": "中等",
                "accuracy": "高"
            }
        }
        
        self.logger = logging.getLogger(__name__)
        
    def get_available_models(self) -> Dict:
        """获取可用模型列表"""
        models_info = {}
        
        for model_id, model_config in self.preset_models.items():
            model_path = self.models_dir / f"{model_id}.pt"
            models_info[model_id] = {
                **model_config,
                "local_path": str(model_path),
                "downloaded": model_path.exists(),
                "file_size": self._get_file_size(model_path) if model_path.exists() else None
            }
        
        return models_info
    
    def _get_file_size(self, file_path: Path) -> str:
        """获取文件大小"""
        try:
            size_bytes = file_path.stat().st_size
            if size_bytes < 1024:
                return f"{size_bytes}B"
            elif size_bytes < 1024**2:
                return f"{size_bytes/1024:.1f}KB"
            elif size_bytes < 1024**3:
                return f"{size_bytes/(1024**2):.1f}MB"
            else:
                return f"{size_bytes/(1024**3):.1f}GB"
        except:
            return "未知"
    
    def download_model(self, model_id: str, progress_callback: Optional[Callable] = None) -> bool:
        """
        下载模型
        
        Args:
            model_id: 模型ID
            progress_callback: 进度回调函数 (downloaded, total, percentage)
            
        Returns:
            下载是否成功
        """
        if model_id not in self.preset_models:
            self.logger.error(f"未知模型ID: {model_id}")
            return False
        
        model_config = self.preset_models[model_id]
        model_path = self.models_dir / f"{model_id}.pt"
        
        # 检查是否已存在
        if model_path.exists():
            self.logger.info(f"模型已存在: {model_path}")
            return True
        
        try:
            self.logger.info(f"开始下载模型: {model_config['name']}")
            
            response = requests.get(model_config['url'], stream=True)
            response.raise_for_status()
            
            total_size = int(response.headers.get('content-length', 0))
            downloaded_size = 0
            
            with open(model_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    if chunk:
                        f.write(chunk)
                        downloaded_size += len(chunk)
                        
                        if progress_callback and total_size > 0:
                            percentage = (downloaded_size / total_size) * 100
                            progress_callback(downloaded_size, total_size, percentage)
            
            self.logger.info(f"模型下载完成: {model_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"下载模型失败: {e}")
            if model_path.exists():
                model_path.unlink()  # 删除不完整的文件
            return False
    
    def train_model(self, model_id: str, dataset_yaml: str, epochs: int = 50,
                   batch_size: int = 8, img_size: int = 640,
                   progress_callback: Optional[Callable] = None,
                   log_callback: Optional[Callable] = None) -> Optional[str]:
        """
        训练模型
        
        Args:
            model_id: 基础模型ID
            dataset_yaml: 数据集配置文件路径
            epochs: 训练轮数
            batch_size: 批次大小
            img_size: 图像尺寸
            progress_callback: 进度回调函数
            
        Returns:
            训练完成的模型路径
        """
        if not ULTRALYTICS_AVAILABLE:
            self.logger.error("ultralytics未安装，无法训练模型")
            return None
        
        if model_id not in self.preset_models:
            self.logger.error(f"未知模型ID: {model_id}")
            return None
        
        model_path = self.models_dir / f"{model_id}.pt"
        if not model_path.exists():
            self.logger.error(f"模型文件不存在: {model_path}")
            return None
        
        if not os.path.exists(dataset_yaml):
            self.logger.error(f"数据集配置文件不存在: {dataset_yaml}")
            return None
        
        try:
            # 创建训练输出目录
            train_dir = self.models_dir / "training" / f"{model_id}_{int(time.time())}"
            train_dir.mkdir(parents=True, exist_ok=True)

            # 设置日志重定向
            if log_callback:
                import sys
                import io
                from contextlib import redirect_stdout, redirect_stderr

                class LogCapture:
                    def __init__(self, callback):
                        self.callback = callback
                        self.buffer = io.StringIO()

                    def write(self, text):
                        if text.strip():
                            self.callback(text.strip())
                        self.buffer.write(text)

                    def flush(self):
                        pass

                log_capture = LogCapture(log_callback)

            # 加载模型
            model = YOLO(str(model_path))

            # 开始训练
            self.logger.info(f"开始训练模型: {model_id}")
            if log_callback:
                log_callback(f"开始训练模型: {model_id}")
                log_callback(f"数据集: {dataset_yaml}")
                log_callback(f"参数: epochs={epochs}, batch={batch_size}, imgsz={img_size}")

            # 重定向输出并训练
            if log_callback:
                with redirect_stdout(log_capture), redirect_stderr(log_capture):
                    results = model.train(
                        data=dataset_yaml,
                        epochs=epochs,
                        batch=batch_size,
                        imgsz=img_size,
                        project=str(train_dir.parent),
                        name=train_dir.name,
                        save=True,
                        save_period=5,  # 每5轮保存一次（种子检测任务）
                        verbose=True,
                        # 种子检测优化参数
                        patience=20,  # 早停耐心值
                        lr0=0.01,  # 初始学习率
                        lrf=0.1,   # 最终学习率因子
                        momentum=0.937,
                        weight_decay=0.0005,
                        warmup_epochs=3,
                        warmup_momentum=0.8,
                        warmup_bias_lr=0.1,
                        # 数据增强（适合种子检测）
                        hsv_h=0.015,  # 色调增强
                        hsv_s=0.7,    # 饱和度增强
                        hsv_v=0.4,    # 明度增强
                        degrees=0.0,  # 旋转角度（种子通常不需要大角度旋转）
                        translate=0.1, # 平移
                        scale=0.5,    # 缩放
                        shear=0.0,    # 剪切（种子检测不需要）
                        perspective=0.0, # 透视变换（种子检测不需要）
                        flipud=0.0,   # 垂直翻转（种子检测不需要）
                        fliplr=0.5,   # 水平翻转
                        mosaic=1.0,   # 马赛克增强
                        mixup=0.0,    # 混合增强（种子检测不适用）
                        copy_paste=0.0 # 复制粘贴增强（种子检测不适用）
                    )
            else:
                results = model.train(
                    data=dataset_yaml,
                    epochs=epochs,
                    batch=batch_size,
                    imgsz=img_size,
                    project=str(train_dir.parent),
                    name=train_dir.name,
                    save=True,
                    save_period=5,  # 每5轮保存一次（种子检测任务）
                    verbose=True,
                    # 种子检测优化参数
                    patience=20,  # 早停耐心值
                    lr0=0.01,  # 初始学习率
                    lrf=0.1,   # 最终学习率因子
                    momentum=0.937,
                    weight_decay=0.0005,
                    warmup_epochs=3,
                    warmup_momentum=0.8,
                    warmup_bias_lr=0.1,
                    # 数据增强（适合种子检测）
                    hsv_h=0.015,  # 色调增强
                    hsv_s=0.7,    # 饱和度增强
                    hsv_v=0.4,    # 明度增强
                    degrees=0.0,  # 旋转角度（种子通常不需要大角度旋转）
                    translate=0.1, # 平移
                    scale=0.5,    # 缩放
                    shear=0.0,    # 剪切（种子检测不需要）
                    perspective=0.0, # 透视变换（种子检测不需要）
                    flipud=0.0,   # 垂直翻转（种子检测不需要）
                    fliplr=0.5,   # 水平翻转
                    mosaic=1.0,   # 马赛克增强
                    mixup=0.0,    # 混合增强（种子检测不适用）
                    copy_paste=0.0 # 复制粘贴增强（种子检测不适用）
                )
            
            # 查找训练完成的模型
            best_model_path = train_dir / "weights" / "best.pt"
            if best_model_path.exists():
                # 复制到模型目录
                trained_model_name = f"{model_id}_trained_{int(time.time())}.pt"
                trained_model_path = self.models_dir / trained_model_name
                shutil.copy2(best_model_path, trained_model_path)
                
                self.logger.info(f"训练完成，模型保存至: {trained_model_path}")
                return str(trained_model_path)
            else:
                self.logger.error("训练完成但未找到最佳模型文件")
                return None
                
        except Exception as e:
            self.logger.error(f"训练模型失败: {e}")
            return None
    
    def predict_image(self, model_path: str, image_path: str, 
                     confidence: float = 0.5, save_dir: Optional[str] = None) -> Optional[Dict]:
        """
        使用模型预测图像
        
        Args:
            model_path: 模型文件路径
            image_path: 图像路径
            confidence: 置信度阈值
            save_dir: 结果保存目录
            
        Returns:
            预测结果字典
        """
        if not ULTRALYTICS_AVAILABLE:
            self.logger.error("ultralytics未安装，无法进行预测")
            return None
        
        if not os.path.exists(model_path):
            self.logger.error(f"模型文件不存在: {model_path}")
            return None
        
        if not os.path.exists(image_path):
            self.logger.error(f"图像文件不存在: {image_path}")
            return None
        
        try:
            # 加载模型
            model = YOLO(model_path)
            
            # 进行预测
            results = model.predict(
                source=image_path,
                conf=confidence,
                save=save_dir is not None,
                project=save_dir,
                name="predictions" if save_dir else None
            )

            # 确定输出路径
            output_path = None
            if save_dir:
                predictions_dir = os.path.join(save_dir, "predictions")
                if os.path.exists(predictions_dir):
                    image_name = os.path.basename(image_path)
                    output_path = os.path.join(predictions_dir, image_name)
                    if not os.path.exists(output_path):
                        # 尝试其他可能的文件名
                        for file in os.listdir(predictions_dir):
                            if file.endswith(('.jpg', '.jpeg', '.png', '.bmp')):
                                output_path = os.path.join(predictions_dir, file)
                                break
            
            # 解析结果
            if results and len(results) > 0:
                result = results[0]
                
                predictions = {
                    'image_path': image_path,
                    'model_path': model_path,
                    'confidence_threshold': confidence,
                    'detections': [],
                    'total_detections': len(result.boxes) if result.boxes is not None else 0,
                    'output_path': output_path
                }
                
                if result.boxes is not None:
                    for i, box in enumerate(result.boxes):
                        detection = {
                            'id': i,
                            'class_id': int(box.cls[0]),
                            'class_name': model.names[int(box.cls[0])],
                            'confidence': float(box.conf[0]),
                            'bbox': box.xyxy[0].tolist(),  # [x1, y1, x2, y2]
                            'center': [(box.xyxy[0][0] + box.xyxy[0][2])/2, 
                                     (box.xyxy[0][1] + box.xyxy[0][3])/2],
                            'area': float((box.xyxy[0][2] - box.xyxy[0][0]) * 
                                        (box.xyxy[0][3] - box.xyxy[0][1]))
                        }
                        predictions['detections'].append(detection)
                
                self.logger.info(f"预测完成，检测到 {predictions['total_detections']} 个对象")
                return predictions
            else:
                self.logger.warning("预测完成但未检测到任何对象")
                return {
                    'image_path': image_path,
                    'model_path': model_path,
                    'confidence_threshold': confidence,
                    'detections': [],
                    'total_detections': 0,
                    'output_path': output_path
                }
                
        except Exception as e:
            self.logger.error(f"预测失败: {e}")
            return None
    
    def get_model_info(self, model_path: str) -> Optional[Dict]:
        """获取模型信息"""
        if not ULTRALYTICS_AVAILABLE:
            return None
        
        try:
            model = YOLO(model_path)
            return {
                'model_path': model_path,
                'task': getattr(model, 'task', 'detect'),
                'classes': getattr(model, 'names', {}),
                'num_classes': len(getattr(model, 'names', {})),
                'input_size': getattr(model, 'imgsz', 640)
            }
        except Exception as e:
            self.logger.error(f"获取模型信息失败: {e}")
            return None
    
    def list_trained_models(self) -> List[str]:
        """列出已训练的模型"""
        trained_models = []

        # 查找复制到主目录的训练模型
        for model_file in self.models_dir.glob("*_trained_*.pt"):
            trained_models.append(str(model_file))

        # 查找training目录下的模型
        training_dir = self.models_dir / "training"
        if training_dir.exists():
            for train_folder in training_dir.iterdir():
                if train_folder.is_dir():
                    weights_dir = train_folder / "weights"
                    if weights_dir.exists():
                        # 查找best.pt和last.pt
                        for weight_file in ["best.pt", "last.pt"]:
                            weight_path = weights_dir / weight_file
                            if weight_path.exists():
                                trained_models.append(str(weight_path))

        return sorted(trained_models)

def main():
    """测试函数"""
    manager = YOLOManager()
    
    # 显示可用模型
    models = manager.get_available_models()
    print("可用模型:")
    for model_id, info in models.items():
        status = "✅ 已下载" if info['downloaded'] else "⬇️ 需下载"
        print(f"  {model_id}: {info['name']} - {info['size']} - {status}")

if __name__ == "__main__":
    main()
