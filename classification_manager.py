#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分类模型管理器
支持ResNet、EfficientNet、MobileNet等常用分类模型
"""

import os
import json
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, Dataset
from torchvision import transforms, models
import torchvision.models as models_module
from pathlib import Path
from typing import Dict, List, Optional, Callable, Tuple
import logging
from PIL import Image
import numpy as np

# 检查是否有可用的分类模型库
try:
    import timm  # 用于EfficientNet等现代模型
    TIMM_AVAILABLE = True
except ImportError:
    TIMM_AVAILABLE = False

class SeedDataset(Dataset):
    """种子分类数据集"""
    
    def __init__(self, data_dir: str, transform=None, class_to_idx=None):
        self.data_dir = Path(data_dir)
        self.transform = transform
        self.samples = []
        self.classes = []
        self.class_to_idx = {}
        
        self._load_dataset(class_to_idx)
    
    def _load_dataset(self, class_to_idx=None):
        """加载数据集"""
        if class_to_idx is None:
            # 自动发现类别
            class_dirs = [d for d in self.data_dir.iterdir() if d.is_dir()]
            self.classes = sorted([d.name for d in class_dirs])
            self.class_to_idx = {cls: idx for idx, cls in enumerate(self.classes)}
        else:
            self.class_to_idx = class_to_idx
            self.classes = list(class_to_idx.keys())
        
        # 加载样本
        for class_name, class_idx in self.class_to_idx.items():
            class_dir = self.data_dir / class_name
            if class_dir.exists():
                for img_path in class_dir.glob('*'):
                    if img_path.suffix.lower() in ['.jpg', '.jpeg', '.png', '.bmp']:
                        self.samples.append((str(img_path), class_idx))
    
    def __len__(self):
        return len(self.samples)
    
    def __getitem__(self, idx):
        img_path, label = self.samples[idx]
        image = Image.open(img_path).convert('RGB')
        
        if self.transform:
            image = self.transform(image)
        
        return image, label

class ClassificationManager:
    """分类模型管理器"""
    
    def __init__(self, models_dir: str = "classification_models"):
        self.models_dir = Path(models_dir)
        self.models_dir.mkdir(exist_ok=True)
        
        self.logger = logging.getLogger(__name__)
        
        # 支持的模型架构
        self.supported_models = {
            # ResNet系列
            'resnet18': {'module': 'torchvision', 'pretrained': True},
            'resnet34': {'module': 'torchvision', 'pretrained': True},
            'resnet50': {'module': 'torchvision', 'pretrained': True},
            'resnet101': {'module': 'torchvision', 'pretrained': True},
            'resnet152': {'module': 'torchvision', 'pretrained': True},
            
            # MobileNet系列
            'mobilenet_v2': {'module': 'torchvision', 'pretrained': True},
            'mobilenet_v3_large': {'module': 'torchvision', 'pretrained': True},
            'mobilenet_v3_small': {'module': 'torchvision', 'pretrained': True},
            
            # EfficientNet系列 (需要timm)
            'efficientnet_b0': {'module': 'timm', 'pretrained': True},
            'efficientnet_b1': {'module': 'timm', 'pretrained': True},
            'efficientnet_b2': {'module': 'timm', 'pretrained': True},
            'efficientnet_b3': {'module': 'timm', 'pretrained': True},
            'efficientnet_b4': {'module': 'timm', 'pretrained': True},
            
            # 其他常用模型
            'densenet121': {'module': 'torchvision', 'pretrained': True},
            'densenet169': {'module': 'torchvision', 'pretrained': True},
            'vgg16': {'module': 'torchvision', 'pretrained': True},
            'vgg19': {'module': 'torchvision', 'pretrained': True},
        }
        
        # 数据变换
        self.train_transform = transforms.Compose([
            transforms.Resize((224, 224)),
            transforms.RandomHorizontalFlip(p=0.5),
            transforms.RandomRotation(degrees=15),
            transforms.ColorJitter(brightness=0.2, contrast=0.2, saturation=0.2, hue=0.1),
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
        ])
        
        self.val_transform = transforms.Compose([
            transforms.Resize((224, 224)),
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
        ])
    
    def get_available_models(self) -> Dict[str, Dict]:
        """获取可用的模型列表"""
        available_models = {}
        
        for model_name, model_info in self.supported_models.items():
            # 检查模型是否可用
            available = True
            if model_info['module'] == 'timm' and not TIMM_AVAILABLE:
                available = False
            
            # 检查是否已下载
            model_path = self.models_dir / f"{model_name}_pretrained.pth"
            downloaded = model_path.exists()
            
            available_models[model_name] = {
                'name': model_name.replace('_', ' ').title(),
                'module': model_info['module'],
                'available': available,
                'downloaded': downloaded,
                'pretrained': model_info['pretrained'],
                'path': str(model_path) if downloaded else None
            }
        
        return available_models
    
    def create_model(self, model_name: str, num_classes: int, pretrained: bool = True) -> nn.Module:
        """创建模型"""
        if model_name not in self.supported_models:
            raise ValueError(f"不支持的模型: {model_name}")
        
        model_info = self.supported_models[model_name]
        
        if model_info['module'] == 'torchvision':
            # 使用torchvision模型
            model_func = getattr(models, model_name)
            model = model_func(pretrained=pretrained)
            
            # 修改最后一层以适应类别数
            if 'resnet' in model_name or 'densenet' in model_name:
                if hasattr(model, 'fc'):
                    model.fc = nn.Linear(model.fc.in_features, num_classes)
                elif hasattr(model, 'classifier'):
                    model.classifier = nn.Linear(model.classifier.in_features, num_classes)
            elif 'mobilenet' in model_name:
                if hasattr(model, 'classifier'):
                    if isinstance(model.classifier, nn.Sequential):
                        model.classifier[-1] = nn.Linear(model.classifier[-1].in_features, num_classes)
                    else:
                        model.classifier = nn.Linear(model.classifier.in_features, num_classes)
            elif 'vgg' in model_name:
                model.classifier[-1] = nn.Linear(model.classifier[-1].in_features, num_classes)
                
        elif model_info['module'] == 'timm' and TIMM_AVAILABLE:
            # 使用timm模型
            model = timm.create_model(model_name, pretrained=pretrained, num_classes=num_classes)
        else:
            raise RuntimeError(f"无法创建模型 {model_name}: 缺少依赖库")
        
        return model
    
    def download_pretrained_model(self, model_name: str) -> bool:
        """下载预训练模型"""
        try:
            self.logger.info(f"下载预训练模型: {model_name}")
            
            # 创建模型（这会自动下载预训练权重）
            model = self.create_model(model_name, num_classes=1000, pretrained=True)
            
            # 保存模型
            model_path = self.models_dir / f"{model_name}_pretrained.pth"
            torch.save(model.state_dict(), model_path)
            
            self.logger.info(f"预训练模型已保存: {model_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"下载预训练模型失败: {e}")
            return False
    
    def prepare_dataset(self, data_dir: str, train_ratio: float = 0.8) -> Tuple[DataLoader, DataLoader, List[str]]:
        """准备数据集"""
        # 创建数据集
        full_dataset = SeedDataset(data_dir, transform=None)
        
        if len(full_dataset) == 0:
            raise ValueError(f"数据目录为空: {data_dir}")
        
        # 分割训练集和验证集
        train_size = int(len(full_dataset) * train_ratio)
        val_size = len(full_dataset) - train_size
        
        train_dataset, val_dataset = torch.utils.data.random_split(
            full_dataset, [train_size, val_size]
        )
        
        # 应用变换
        train_dataset.dataset.transform = self.train_transform
        val_dataset.dataset.transform = self.val_transform
        
        # 创建数据加载器
        train_loader = DataLoader(train_dataset, batch_size=32, shuffle=True, num_workers=2)
        val_loader = DataLoader(val_dataset, batch_size=32, shuffle=False, num_workers=2)
        
        return train_loader, val_loader, full_dataset.classes
    
    def list_trained_models(self) -> List[str]:
        """列出已训练的模型"""
        trained_models = []
        
        for model_file in self.models_dir.glob("*_trained_*.pth"):
            trained_models.append(str(model_file))
        
        return sorted(trained_models)

def main():
    """测试函数"""
    manager = ClassificationManager()
    
    # 显示可用模型
    models = manager.get_available_models()
    print("可用分类模型:")
    for model_id, info in models.items():
        status = "✅ 可用" if info['available'] else "❌ 不可用"
        downloaded = "✅ 已下载" if info['downloaded'] else "⬇️ 需下载"
        print(f"  {model_id}: {info['name']} - {status} - {downloaded}")

if __name__ == "__main__":
    main()
