#!/usr/bin/env python3
"""
快速测试种子检测功能
"""

def test_imports():
    """测试导入"""
    print("测试导入...")
    
    try:
        from seed_detection_optimizer import SeedDetectionOptimizer
        print("✅ 种子检测优化器导入成功")
    except Exception as e:
        print(f"❌ 种子检测优化器导入失败: {e}")
        return False
    
    try:
        from yolo_manager import YOLOManager
        print("✅ YOLO管理器导入成功")
    except Exception as e:
        print(f"❌ YOLO管理器导入失败: {e}")
        return False
    
    try:
        from yolo_gui_components import YOLOTrainingPanel
        print("✅ YOLO训练面板导入成功")
    except Exception as e:
        print(f"❌ YOLO训练面板导入失败: {e}")
        return False
    
    return True

def test_optimizer():
    """测试优化器"""
    print("\n测试优化器...")
    
    try:
        from seed_detection_optimizer import SeedDetectionOptimizer
        optimizer = SeedDetectionOptimizer()
        print(f"✅ 优化器初始化成功")
        print(f"   种子类别: {optimizer.seed_class_name}")
        print(f"   类别ID: {optimizer.class_id}")
        return True
    except Exception as e:
        print(f"❌ 优化器测试失败: {e}")
        return False

def test_yolo_manager():
    """测试YOLO管理器"""
    print("\n测试YOLO管理器...")
    
    try:
        from yolo_manager import YOLOManager
        manager = YOLOManager()
        print("✅ YOLO管理器初始化成功")
        
        # 检查默认参数
        import inspect
        sig = inspect.signature(manager.train_model)
        params = sig.parameters
        
        epochs_default = params['epochs'].default
        batch_size_default = params['batch_size'].default
        
        print(f"   默认训练轮数: {epochs_default}")
        print(f"   默认批次大小: {batch_size_default}")
        
        return True
    except Exception as e:
        print(f"❌ YOLO管理器测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🌱 种子检测功能快速测试")
    print("=" * 40)
    
    tests = [
        test_imports,
        test_optimizer,
        test_yolo_manager
    ]
    
    passed = 0
    for test in tests:
        if test():
            passed += 1
    
    print(f"\n结果: {passed}/{len(tests)} 项测试通过")
    
    if passed == len(tests):
        print("🎉 种子检测功能准备就绪！")
    else:
        print("⚠️ 部分功能存在问题")

if __name__ == "__main__":
    main()
