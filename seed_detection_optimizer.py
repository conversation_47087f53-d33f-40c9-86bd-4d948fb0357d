#!/usr/bin/env python3
"""
种子检测YOLO优化器
专门针对种子检测任务优化YOLO训练配置和数据集
"""

import os
import json
import shutil
import random
from pathlib import Path
from typing import Dict, List, Tuple, Optional

class SeedDetectionOptimizer:
    """种子检测优化器"""
    
    def __init__(self):
        self.seed_class_name = "seed"
        self.class_id = 0  # 种子类别ID固定为0
        
    def optimize_dataset_for_seed_detection(self, source_dir: str, output_dir: str, 
                                          train_ratio: float = 0.8) -> Optional[Dict]:
        """
        为种子检测优化数据集
        
        Args:
            source_dir: 源数据目录（包含分割结果）
            output_dir: 输出数据集目录
            train_ratio: 训练集比例
            
        Returns:
            数据集信息字典
        """
        print(f"🌱 开始优化种子检测数据集")
        print(f"源目录: {source_dir}")
        print(f"输出目录: {output_dir}")
        
        # 创建数据集目录结构
        dataset_dirs = self._create_dataset_structure(output_dir)
        
        # 查找所有分割结果
        segmentation_results = self._find_segmentation_results(source_dir)
        
        if not segmentation_results:
            print("❌ 未找到分割结果")
            return None
            
        print(f"📊 找到 {len(segmentation_results)} 个分割结果")
        
        # 过滤和验证数据
        valid_results = self._filter_valid_results(segmentation_results)
        print(f"✅ 有效结果: {len(valid_results)} 个")
        
        if not valid_results:
            print("❌ 没有有效的分割结果")
            return None
        
        # 分割训练集和验证集
        train_results, val_results = self._split_dataset(valid_results, train_ratio)
        
        # 处理训练集
        train_count = self._process_dataset_split(train_results, dataset_dirs, 'train')
        
        # 处理验证集
        val_count = self._process_dataset_split(val_results, dataset_dirs, 'val')
        
        # 创建优化的数据集配置
        config_file = self._create_optimized_dataset_config(output_dir, train_count, val_count)
        
        # 创建数据集统计信息
        stats = self._create_dataset_statistics(output_dir, train_results + val_results)
        
        dataset_info = {
            'dataset_dir': output_dir,
            'train_images': train_count,
            'val_images': val_count,
            'total_images': train_count + val_count,
            'config_file': config_file,
            'class_name': self.seed_class_name,
            'optimization': 'seed_detection_specialized',
            'statistics': stats
        }
        
        print(f"🎯 种子检测数据集优化完成!")
        print(f"   训练图像: {train_count}")
        print(f"   验证图像: {val_count}")
        print(f"   配置文件: {config_file}")
        
        return dataset_info
    
    def _create_dataset_structure(self, output_dir: str) -> Dict:
        """创建数据集目录结构"""
        dataset_dirs = {
            'images': {
                'train': os.path.join(output_dir, 'images', 'train'),
                'val': os.path.join(output_dir, 'images', 'val')
            },
            'labels': {
                'train': os.path.join(output_dir, 'labels', 'train'),
                'val': os.path.join(output_dir, 'labels', 'val')
            }
        }
        
        for split_dirs in dataset_dirs.values():
            for dir_path in split_dirs.values():
                os.makedirs(dir_path, exist_ok=True)
                
        return dataset_dirs
    
    def _find_segmentation_results(self, source_dir: str) -> List[Tuple[str, str, str]]:
        """查找分割结果"""
        results = []
        
        for root, dirs, files in os.walk(source_dir):
            for file in files:
                if file.endswith('_segmentation_results.json'):
                    json_path = os.path.join(root, file)
                    
                    # 查找对应的原始图像
                    base_name = file.replace('_segmentation_results.json', '')
                    
                    # 查找原始图像文件
                    image_path = None
                    for ext in ['.jpg', '.jpeg', '.png', '.bmp', '.tiff']:
                        potential_image = os.path.join(root, base_name + ext)
                        if os.path.exists(potential_image):
                            image_path = potential_image
                            break
                    
                    if image_path:
                        results.append((json_path, image_path, base_name))
                        
        return results
    
    def _filter_valid_results(self, results: List[Tuple[str, str, str]]) -> List[Tuple[str, str, str]]:
        """过滤有效的分割结果"""
        valid_results = []
        
        for json_path, image_path, base_name in results:
            try:
                # 检查JSON文件
                with open(json_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                # 检查是否有种子检测结果
                if 'seeds' in data and len(data['seeds']) > 0:
                    # 检查图像文件是否存在
                    if os.path.exists(image_path):
                        valid_results.append((json_path, image_path, base_name))
                        
            except Exception as e:
                print(f"⚠️ 跳过无效结果 {json_path}: {e}")
                
        return valid_results
    
    def _split_dataset(self, results: List[Tuple[str, str, str]], 
                      train_ratio: float) -> Tuple[List, List]:
        """分割数据集"""
        random.shuffle(results)
        split_idx = int(len(results) * train_ratio)
        train_results = results[:split_idx]
        val_results = results[split_idx:]
        
        print(f"📊 数据集分割: 训练集 {len(train_results)}, 验证集 {len(val_results)}")
        
        return train_results, val_results
    
    def _process_dataset_split(self, results: List[Tuple[str, str, str]], 
                              dataset_dirs: Dict, split: str) -> int:
        """处理数据集分割"""
        count = 0
        
        for json_path, image_path, base_name in results:
            try:
                # 复制图像文件
                image_filename = os.path.basename(image_path)
                dest_image_path = os.path.join(dataset_dirs['images'][split], image_filename)
                shutil.copy2(image_path, dest_image_path)
                
                # 创建YOLO标注文件
                label_filename = f"{base_name}.txt"
                dest_label_path = os.path.join(dataset_dirs['labels'][split], label_filename)
                
                if self._create_yolo_label(json_path, dest_label_path):
                    count += 1
                    
            except Exception as e:
                print(f"⚠️ 处理文件失败 {json_path}: {e}")
                
        return count
    
    def _create_yolo_label(self, json_path: str, output_path: str) -> bool:
        """创建YOLO标注文件"""
        try:
            with open(json_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            image_width = data.get('image_width', 1)
            image_height = data.get('image_height', 1)
            
            with open(output_path, 'w') as f:
                for seed in data.get('seeds', []):
                    bbox = seed.get('bbox')
                    if bbox:
                        x, y, w, h = bbox
                        
                        # 转换为YOLO格式（归一化的中心点坐标和宽高）
                        center_x = (x + w / 2) / image_width
                        center_y = (y + h / 2) / image_height
                        norm_width = w / image_width
                        norm_height = h / image_height
                        
                        # 写入YOLO格式: class_id center_x center_y width height
                        f.write(f"{self.class_id} {center_x:.6f} {center_y:.6f} {norm_width:.6f} {norm_height:.6f}\n")
            
            return True
            
        except Exception as e:
            print(f"创建YOLO标注失败 {json_path}: {e}")
            return False
    
    def _create_optimized_dataset_config(self, output_dir: str, 
                                       train_count: int, val_count: int) -> str:
        """创建优化的数据集配置文件"""
        yaml_content = f"""# 种子检测专用YOLO数据集配置
# 由透明背景种子分割系统自动生成并优化

# 数据集路径
path: {os.path.abspath(output_dir)}
train: images/train
val: images/val

# 类别配置（种子检测专用）
nc: 1
names: ['{self.seed_class_name}']

# 数据集统计
train_images: {train_count}
val_images: {val_count}
total_images: {train_count + val_count}

# 种子检测优化配置
task: 'seed_detection'
optimization_level: 'specialized'
target_objects: 'seeds'

# 训练建议参数
recommended_params:
  epochs: 50
  batch_size: 8
  img_size: 640
  patience: 20
  lr0: 0.01
  lrf: 0.1

# 生成信息
generated_by: "Seed Detection Optimizer"
description: "专门针对种子检测任务优化的YOLO数据集"
optimization_date: "{Path().cwd()}"
"""
        
        yaml_path = os.path.join(output_dir, 'dataset.yaml')
        with open(yaml_path, 'w', encoding='utf-8') as f:
            f.write(yaml_content)
        
        return yaml_path
    
    def _create_dataset_statistics(self, output_dir: str, 
                                 all_results: List[Tuple[str, str, str]]) -> Dict:
        """创建数据集统计信息"""
        stats = {
            'total_images': len(all_results),
            'total_seeds': 0,
            'avg_seeds_per_image': 0,
            'seed_size_distribution': {'small': 0, 'medium': 0, 'large': 0}
        }
        
        total_seeds = 0
        for json_path, _, _ in all_results:
            try:
                with open(json_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                seeds = data.get('seeds', [])
                total_seeds += len(seeds)
                
                # 统计种子大小分布
                for seed in seeds:
                    bbox = seed.get('bbox')
                    if bbox:
                        _, _, w, h = bbox
                        area = w * h
                        if area < 1000:
                            stats['seed_size_distribution']['small'] += 1
                        elif area < 5000:
                            stats['seed_size_distribution']['medium'] += 1
                        else:
                            stats['seed_size_distribution']['large'] += 1
                            
            except Exception:
                continue
        
        stats['total_seeds'] = total_seeds
        stats['avg_seeds_per_image'] = total_seeds / len(all_results) if all_results else 0
        
        # 保存统计信息
        stats_path = os.path.join(output_dir, 'dataset_statistics.json')
        with open(stats_path, 'w', encoding='utf-8') as f:
            json.dump(stats, f, indent=2, ensure_ascii=False)
        
        return stats

def main():
    """测试函数"""
    optimizer = SeedDetectionOptimizer()
    
    # 示例用法
    source_dir = "output"  # 分割结果目录
    output_dir = "optimized_seed_dataset"  # 优化后的数据集目录
    
    dataset_info = optimizer.optimize_dataset_for_seed_detection(
        source_dir=source_dir,
        output_dir=output_dir,
        train_ratio=0.8
    )
    
    if dataset_info:
        print("✅ 种子检测数据集优化成功!")
        print(f"数据集信息: {dataset_info}")
    else:
        print("❌ 数据集优化失败")

if __name__ == "__main__":
    main()
