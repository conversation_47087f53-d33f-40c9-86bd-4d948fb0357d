#!/usr/bin/env python3
"""
测试YOLO训练功能的简单脚本
"""

import os
import sys
from pathlib import Path

def test_yolo_training():
    """测试YOLO训练功能"""
    print("=== 测试YOLO训练功能 ===")
    
    try:
        from ultralytics import YOLO
        print("✓ Ultralytics导入成功")
        
        # 测试加载模型
        model_path = "yolo_models/yolov8n.pt"
        if os.path.exists(model_path):
            print(f"✓ 找到模型文件: {model_path}")
            
            try:
                model = YOLO(model_path)
                print("✓ 模型加载成功")
                print(f"  模型任务: {getattr(model, 'task', 'unknown')}")
                print(f"  类别数量: {len(getattr(model, 'names', {}))}")
                
                # 检查是否有数据集配置文件
                dataset_files = []
                for root, dirs, files in os.walk("."):
                    for file in files:
                        if file == "dataset.yaml":
                            dataset_files.append(os.path.join(root, file))
                
                if dataset_files:
                    print(f"✓ 找到数据集配置文件: {len(dataset_files)} 个")
                    for dataset_file in dataset_files:
                        print(f"  - {dataset_file}")
                        
                        # 检查数据集文件内容
                        try:
                            with open(dataset_file, 'r', encoding='utf-8') as f:
                                content = f.read()
                                print(f"    数据集配置预览:")
                                lines = content.split('\n')[:10]
                                for line in lines:
                                    if line.strip():
                                        print(f"      {line}")
                                if len(content.split('\n')) > 10:
                                    print("      ...")
                        except Exception as e:
                            print(f"    ✗ 读取数据集配置失败: {e}")
                else:
                    print("⚠ 未找到数据集配置文件 (dataset.yaml)")
                    print("  请先进行图像分割并生成YOLO数据集")
                
                return True
                
            except Exception as e:
                print(f"✗ 模型加载失败: {e}")
                return False
        else:
            print(f"✗ 模型文件不存在: {model_path}")
            return False
            
    except ImportError as e:
        print(f"✗ Ultralytics导入失败: {e}")
        return False

def main():
    """主函数"""
    print("YOLO训练功能测试")
    print("=" * 50)
    
    success = test_yolo_training()
    
    print("\n=== 测试结果 ===")
    if success:
        print("✓ YOLO训练功能准备就绪")
        print("\n建议:")
        print("1. 确保已完成图像分割")
        print("2. 确保已生成YOLO数据集")
        print("3. 在GUI中选择数据集并开始训练")
    else:
        print("✗ YOLO训练功能存在问题")
        print("\n请检查:")
        print("1. 模型文件是否完整")
        print("2. Ultralytics是否正确安装")
        print("3. 环境配置是否正确")

if __name__ == "__main__":
    main()
