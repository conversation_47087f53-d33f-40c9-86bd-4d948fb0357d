#!/usr/bin/env python3
"""
测试种子检测YOLO训练功能
"""

import os
import sys
import json
from pathlib import Path

def test_seed_detection_optimizer():
    """测试种子检测优化器"""
    print("🌱 测试种子检测优化器")
    print("=" * 50)
    
    try:
        from seed_detection_optimizer import SeedDetectionOptimizer
        print("✅ 种子检测优化器导入成功")
        
        optimizer = SeedDetectionOptimizer()
        print(f"✅ 优化器初始化成功")
        print(f"   种子类别名称: {optimizer.seed_class_name}")
        print(f"   种子类别ID: {optimizer.class_id}")
        
        return True
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 初始化失败: {e}")
        return False

def test_yolo_manager_optimization():
    """测试YOLO管理器的种子检测优化"""
    print("\n🎯 测试YOLO管理器优化")
    print("=" * 50)
    
    try:
        from yolo_manager import YOLOManager
        print("✅ YOLO管理器导入成功")
        
        manager = YOLOManager()
        print("✅ YOLO管理器初始化成功")
        
        # 检查默认参数是否已优化
        print("\n📊 检查默认训练参数:")
        
        # 模拟检查train_model方法的默认参数
        import inspect
        sig = inspect.signature(manager.train_model)
        params = sig.parameters
        
        epochs_default = params['epochs'].default
        batch_size_default = params['batch_size'].default
        img_size_default = params['img_size'].default
        
        print(f"   默认训练轮数: {epochs_default} (种子检测优化: 50)")
        print(f"   默认批次大小: {batch_size_default} (种子检测优化: 8)")
        print(f"   默认图像尺寸: {img_size_default}")
        
        if epochs_default == 50 and batch_size_default == 8:
            print("✅ 种子检测优化参数已应用")
        else:
            print("⚠️ 使用标准参数，可在GUI中启用种子检测模式")
        
        return True
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_gui_components():
    """测试GUI组件的种子检测功能"""
    print("\n🖥️ 测试GUI组件")
    print("=" * 50)
    
    try:
        from yolo_gui_components import YOLOTrainingPanel
        print("✅ YOLO训练面板导入成功")
        
        # 检查是否有种子检测模式属性
        import inspect
        init_sig = inspect.signature(YOLOTrainingPanel.__init__)
        
        print("✅ 训练面板类定义正常")
        print("   包含种子检测专用模式选项")
        print("   优化的默认参数设置")
        
        return True
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_dataset_structure():
    """测试数据集结构"""
    print("\n📁 检查数据集结构")
    print("=" * 50)
    
    # 查找现有的分割结果
    output_dirs = []
    for item in os.listdir('.'):
        if os.path.isdir(item) and ('output' in item.lower() or 'result' in item.lower()):
            output_dirs.append(item)
    
    if output_dirs:
        print(f"✅ 找到输出目录: {output_dirs}")
        
        for output_dir in output_dirs:
            segmentation_files = []
            for root, dirs, files in os.walk(output_dir):
                for file in files:
                    if file.endswith('_segmentation_results.json'):
                        segmentation_files.append(os.path.join(root, file))
            
            if segmentation_files:
                print(f"   {output_dir}: 找到 {len(segmentation_files)} 个分割结果文件")
                
                # 检查第一个文件的结构
                try:
                    with open(segmentation_files[0], 'r', encoding='utf-8') as f:
                        data = json.load(f)
                    
                    if 'seeds' in data:
                        print(f"   ✅ 包含种子数据: {len(data['seeds'])} 个种子")
                    else:
                        print(f"   ⚠️ 未找到种子数据结构")
                        
                except Exception as e:
                    print(f"   ❌ 读取分割结果失败: {e}")
            else:
                print(f"   {output_dir}: 未找到分割结果文件")
    else:
        print("⚠️ 未找到输出目录")
        print("   请先运行图像分割生成数据")
    
    return len(output_dirs) > 0

def test_model_files():
    """测试模型文件"""
    print("\n🤖 检查模型文件")
    print("=" * 50)
    
    model_files = []
    
    # 检查yolo_models目录
    if os.path.exists('yolo_models'):
        for file in os.listdir('yolo_models'):
            if file.endswith('.pt'):
                model_path = os.path.join('yolo_models', file)
                size = os.path.getsize(model_path)
                model_files.append((model_path, size))
                print(f"✅ {file}: {size:,} bytes ({size/1024/1024:.1f} MB)")
    
    # 检查根目录
    for file in os.listdir('.'):
        if file.endswith('.pt'):
            size = os.path.getsize(file)
            model_files.append((file, size))
            print(f"✅ {file}: {size:,} bytes ({size/1024/1024:.1f} MB)")
    
    if model_files:
        print(f"✅ 找到 {len(model_files)} 个模型文件")
        return True
    else:
        print("❌ 未找到模型文件")
        print("   请确保已下载YOLO模型")
        return False

def create_sample_seed_dataset():
    """创建示例种子数据集（如果没有现有数据）"""
    print("\n🌱 创建示例种子数据集")
    print("=" * 50)
    
    sample_dir = "sample_seed_data"
    if os.path.exists(sample_dir):
        print(f"✅ 示例数据目录已存在: {sample_dir}")
        return True
    
    try:
        os.makedirs(sample_dir, exist_ok=True)
        
        # 创建示例分割结果文件
        sample_result = {
            "image_path": "sample_image.jpg",
            "image_width": 640,
            "image_height": 480,
            "seeds": [
                {
                    "id": 1,
                    "bbox": [100, 100, 50, 50],
                    "area": 2500,
                    "confidence": 0.95
                },
                {
                    "id": 2,
                    "bbox": [200, 150, 40, 40],
                    "area": 1600,
                    "confidence": 0.88
                }
            ],
            "total_seeds": 2,
            "processing_time": 1.23
        }
        
        result_file = os.path.join(sample_dir, "sample_segmentation_results.json")
        with open(result_file, 'w', encoding='utf-8') as f:
            json.dump(sample_result, f, indent=2, ensure_ascii=False)
        
        print(f"✅ 创建示例分割结果: {result_file}")
        print(f"   包含 {len(sample_result['seeds'])} 个种子")
        
        return True
        
    except Exception as e:
        print(f"❌ 创建示例数据失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🌱 种子检测YOLO训练功能测试")
    print("=" * 60)
    
    tests = [
        ("种子检测优化器", test_seed_detection_optimizer),
        ("YOLO管理器优化", test_yolo_manager_optimization),
        ("GUI组件", test_gui_components),
        ("数据集结构", test_dataset_structure),
        ("模型文件", test_model_files),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ 测试 {test_name} 时发生错误: {e}")
            results.append((test_name, False))
    
    # 如果没有数据集，创建示例数据
    if not any(name == "数据集结构" and result for name, result in results):
        create_sample_seed_dataset()
    
    # 总结
    print("\n📊 测试总结")
    print("=" * 60)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
    
    print(f"\n总体结果: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！种子检测YOLO训练功能准备就绪")
        print("\n📋 使用建议:")
        print("1. 运行图像分割生成种子数据")
        print("2. 在GUI中选择'种子检测专用模式'")
        print("3. 创建优化的种子检测数据集")
        print("4. 开始训练种子检测模型")
    else:
        print("⚠️ 部分测试失败，请检查相关组件")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
