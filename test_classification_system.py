#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试完整的分类系统
包括分类训练和分类识别功能
"""

import os
import sys
import json
from pathlib import Path

def test_classification_manager():
    """测试分类管理器"""
    print("🧠 测试分类管理器")
    print("=" * 50)
    
    try:
        from classification_manager import ClassificationManager
        manager = ClassificationManager()
        print("✅ 分类管理器初始化成功")
        
        # 获取可用模型
        models = manager.get_available_models()
        print(f"✅ 找到 {len(models)} 个支持的模型")
        
        # 显示模型信息
        available_count = 0
        downloaded_count = 0
        
        for model_name, info in models.items():
            status = "✅ 可用" if info['available'] else "❌ 不可用"
            downloaded = "✅ 已下载" if info['downloaded'] else "⬇️ 需下载"
            
            if info['available']:
                available_count += 1
            if info['downloaded']:
                downloaded_count += 1
            
            print(f"  {model_name}: {info['name']} - {status} - {downloaded}")
        
        print(f"\n📊 模型统计:")
        print(f"  可用模型: {available_count}/{len(models)}")
        print(f"  已下载模型: {downloaded_count}/{len(models)}")
        
        return True
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_classification_trainer():
    """测试分类训练器"""
    print("\n🏋️ 测试分类训练器")
    print("=" * 50)
    
    try:
        from classification_manager import ClassificationManager
        from classification_trainer import ClassificationTrainer
        
        manager = ClassificationManager()
        trainer = ClassificationTrainer(manager)
        print("✅ 分类训练器初始化成功")
        
        # 检查是否有分割结果可用于测试
        test_dirs = ["output", "images_test/output", "test_output"]
        segmentation_found = False
        
        for test_dir in test_dirs:
            if os.path.exists(test_dir):
                # 查找分割结果文件
                for root, dirs, files in os.walk(test_dir):
                    for file in files:
                        if file.endswith('_segmentation_results.json'):
                            segmentation_found = True
                            print(f"✅ 找到分割结果: {os.path.join(root, file)}")
                            break
                    if segmentation_found:
                        break
            if segmentation_found:
                break
        
        if segmentation_found:
            print("✅ 可以进行分类数据准备测试")
        else:
            print("⚠️ 未找到分割结果，无法测试数据准备功能")
        
        return True
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_classification_recognizer():
    """测试分类识别器"""
    print("\n🔍 测试分类识别器")
    print("=" * 50)
    
    try:
        from classification_manager import ClassificationManager
        from classification_recognizer import ClassificationRecognizer
        
        # 检查YOLO管理器
        try:
            from yolo_manager import YOLOManager
            yolo_manager = YOLOManager()
            print("✅ YOLO管理器可用")
        except Exception as e:
            print(f"❌ YOLO管理器不可用: {e}")
            return False
        
        classification_manager = ClassificationManager()
        recognizer = ClassificationRecognizer(yolo_manager, classification_manager)
        print("✅ 分类识别器初始化成功")
        
        # 检查训练好的分类模型
        trained_models = classification_manager.list_trained_models()
        if trained_models:
            print(f"✅ 找到 {len(trained_models)} 个训练好的分类模型")
            for model in trained_models[:3]:
                print(f"  - {os.path.basename(model)}")
        else:
            print("⚠️ 未找到训练好的分类模型")
        
        # 检查YOLO模型
        yolo_models = yolo_manager.list_trained_models()
        if yolo_models:
            print(f"✅ 找到 {len(yolo_models)} 个训练好的YOLO模型")
            for model in yolo_models[:3]:
                print(f"  - {os.path.basename(model)}")
        else:
            print("⚠️ 未找到训练好的YOLO模型")
        
        return True
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_gui_components():
    """测试GUI组件"""
    print("\n🖥️ 测试GUI组件")
    print("=" * 50)
    
    try:
        from classification_gui_components import ClassificationTrainingPanel, ClassificationRecognitionPanel
        print("✅ 分类GUI组件导入成功")
        
        # 检查组件类定义
        import inspect
        
        # 检查训练面板
        training_methods = [method for method in dir(ClassificationTrainingPanel) 
                          if not method.startswith('_')]
        print(f"✅ 分类训练面板包含 {len(training_methods)} 个公共方法")
        
        # 检查识别面板
        recognition_methods = [method for method in dir(ClassificationRecognitionPanel) 
                             if not method.startswith('_')]
        print(f"✅ 分类识别面板包含 {len(recognition_methods)} 个公共方法")
        
        return True
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_main_gui_integration():
    """测试主界面集成"""
    print("\n🏠 测试主界面集成")
    print("=" * 50)
    
    try:
        # 检查主界面是否包含新的方法
        with open('enhanced_segmentation_gui.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        required_methods = [
            'create_classification_training_interface',
            'create_classification_recognition_interface',
            'classification_manager',
            'classification_trainer',
            'classification_recognizer'
        ]
        
        missing_methods = []
        for method in required_methods:
            if method not in content:
                missing_methods.append(method)
        
        if missing_methods:
            print(f"❌ 主界面缺少以下组件: {missing_methods}")
            return False
        else:
            print("✅ 主界面包含所有必需的分类组件")
        
        # 检查标签页名称修改
        if '目标检测' in content and 'YOLO训练' in content:
            print("✅ 标签页名称已正确修改")
        else:
            print("⚠️ 标签页名称可能未完全修改")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def create_sample_classification_data():
    """创建示例分类数据"""
    print("\n📁 创建示例分类数据")
    print("=" * 50)
    
    try:
        # 创建示例分类数据目录结构
        sample_dir = Path("sample_classification_data")
        sample_dir.mkdir(exist_ok=True)
        
        # 创建几个种子类别目录
        seed_classes = ["wheat", "corn", "rice", "soybean"]
        
        for seed_class in seed_classes:
            class_dir = sample_dir / seed_class
            class_dir.mkdir(exist_ok=True)
            
            # 创建示例数据集信息
            sample_info = {
                "class_name": seed_class,
                "sample_count": 0,
                "description": f"Sample {seed_class} seeds for classification training"
            }
            
            info_file = class_dir / "class_info.json"
            with open(info_file, 'w', encoding='utf-8') as f:
                json.dump(sample_info, f, indent=2, ensure_ascii=False)
        
        # 创建数据集总体信息
        dataset_info = {
            "dataset_name": "Sample Seed Classification Dataset",
            "classes": seed_classes,
            "num_classes": len(seed_classes),
            "total_samples": 0,
            "created_by": "Classification System Test",
            "description": "Sample dataset for testing classification training"
        }
        
        dataset_info_file = sample_dir / "dataset_info.json"
        with open(dataset_info_file, 'w', encoding='utf-8') as f:
            json.dump(dataset_info, f, indent=2, ensure_ascii=False)
        
        print(f"✅ 创建示例分类数据目录: {sample_dir}")
        print(f"   包含 {len(seed_classes)} 个种子类别")
        print(f"   类别: {', '.join(seed_classes)}")
        
        return True
        
    except Exception as e:
        print(f"❌ 创建示例数据失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🌱 完整分类系统测试")
    print("=" * 60)
    
    tests = [
        ("分类管理器", test_classification_manager),
        ("分类训练器", test_classification_trainer),
        ("分类识别器", test_classification_recognizer),
        ("GUI组件", test_gui_components),
        ("主界面集成", test_main_gui_integration),
        ("示例数据创建", create_sample_classification_data),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ 测试 {test_name} 时发生错误: {e}")
            results.append((test_name, False))
    
    # 总结
    print("\n📊 测试总结")
    print("=" * 60)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
    
    print(f"\n总体结果: {passed}/{total} 项测试通过")
    
    if passed >= total - 1:  # 允许1个测试失败
        print("🎉 分类系统集成成功！")
        print("\n📋 系统功能:")
        print("✅ 图像分割 → 种子检测")
        print("✅ YOLO训练 → 目标检测")
        print("✅ 分类训练 → 种子分类")
        print("✅ 分类识别 → 检测+分类")
        print("\n🚀 现在可以使用完整的种子分析系统了！")
        
        print("\n📖 使用流程:")
        print("1. 图像分割 → 生成种子分割结果")
        print("2. YOLO训练 → 训练种子检测模型")
        print("3. 分类训练 → 基于分割结果训练分类模型")
        print("4. 分类识别 → YOLO检测 + 分类识别")
    else:
        print("⚠️ 部分功能需要进一步调试")
    
    return passed >= total - 1

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
